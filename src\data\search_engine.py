"""
搜索引擎封装模块
支持 DuckDuckGo、Sogou 和 Tavily 三种搜索方式
"""

import time
import json
import os
from typing import List, Dict, Any
from duckduckgo_search import DDGS

# 尝试导入搜狗搜索，如果失败则只支持DDG
try:
    from sogou_search import sogou_search
    SOGOU_AVAILABLE = True
except ImportError:
    SOGOU_AVAILABLE = False

# 尝试导入Tavily搜索，如果失败则不支持Tavily
try:
    from tavily import TavilyClient
    TAVILY_AVAILABLE = True
except ImportError:
    TAVILY_AVAILABLE = False

class SearchEngine:
    """搜索引擎封装类"""

    def __init__(self, engine: str = "ddg", tavily_api_key: str = None):
        """
        初始化搜索引擎

        Args:
            engine: 搜索引擎类型，支持 "ddg" (DuckDuckGo)、"sogou" (搜狗) 和 "tavily" (Tavily)
            tavily_api_key: Tavily API密钥，使用Tavily时必需
        """
        self.engine = engine.lower()
        self.delay = 2.0  # 增加搜索延迟避免速率限制
        self.search_client = None  # 搜索客户端实例
        self.tavily_api_key = tavily_api_key or "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"

        if self.engine not in ["ddg", "sogou", "tavily"]:
            raise ValueError(f"不支持的搜索引擎: {engine}. 支持的类型: 'ddg', 'sogou', 'tavily'")

        if self.engine == "sogou" and not SOGOU_AVAILABLE:
            print("警告: 搜狗搜索 (k_sogou_search) 未安装, 将回退到 DuckDuckGo。")
            self.engine = "ddg"

        if self.engine == "tavily" and not TAVILY_AVAILABLE:
            print("警告: Tavily搜索 (tavily-python) 未安装, 将回退到 DuckDuckGo。")
            self.engine = "ddg"

        # 初始化搜索客户端
        if self.engine == "ddg":
            self.search_client = DDGS()
        elif self.engine == "tavily" and TAVILY_AVAILABLE:
            self.search_client = TavilyClient(api_key=self.tavily_api_key)

    def search(self, keywords: str, max_results: int = 10, save_to_json: str = None) -> List[Dict[str, Any]]:
        """
        统一搜索接口

        Args:
            keywords: 搜索关键词
            max_results: 最大结果数
            save_to_json: 如果提供文件名，则将结果保存为JSON文件

        Returns:
            搜索结果列表，每个结果包含 title, url, description 字段
        """
        print(f"使用 {self.engine.upper()} 搜索引擎搜索: '{keywords}'")
        try:
            if self.engine == "ddg":
                results = self._search_ddg(keywords, max_results)
            elif self.engine == "sogou":
                results = self._search_sogou(keywords, max_results)
            elif self.engine == "tavily":
                results = self._search_tavily(keywords, max_results)
            else:
                results = []
            
            # 如果指定了保存文件名，则保存结果
            if save_to_json and results:
                self.save_results_to_json(results, save_to_json)
            
            time.sleep(self.delay)
            return results
        except Exception as e:
            print(f"搜索失败 ({self.engine}): {e}")
            # 如果搜狗或Tavily失败，可以考虑回退
            if self.engine in ["sogou", "tavily"]:
                print(f"{self.engine.upper()}搜索失败，尝试回退到 DuckDuckGo...")
                self.engine = "ddg"
                self.search_client = DDGS()  # 重新初始化DDG客户端
                return self.search(keywords, max_results, save_to_json)
            return []

    def _search_ddg(self, keywords: str, max_results: int) -> List[Dict[str, Any]]:
        """DuckDuckGo 搜索"""
        # 使用同一个search_client实例
        if self.search_client is None:
            self.search_client = DDGS()

        results = self.search_client.text(
            keywords=keywords,
            region="cn-zh",
            max_results=max_results
        )
        # 标准化结果格式
        return [
            {
                'title': r.get('title', '无标题'),
                'url': r.get('href', '无链接'),
                'description': r.get('body', '无摘要')
            }
            for r in results
        ]

    def _search_sogou(self, keywords: str, max_results: int) -> List[Dict[str, Any]]:
        """搜狗搜索"""
        if not SOGOU_AVAILABLE:
            return []
        return sogou_search(keywords, num_results=max_results)

    def _search_tavily(self, keywords: str, max_results: int) -> List[Dict[str, Any]]:
        """Tavily 搜索"""
        # 使用同一个search_client实例
        if self.search_client is None:
            if TAVILY_AVAILABLE:
                try:
                    self.search_client = TavilyClient(api_key=self.tavily_api_key)
                    print(f"Tavily客户端初始化成功，API密钥: {self.tavily_api_key[:10]}...")
                except Exception as e:
                    print(f"Tavily客户端初始化失败: {e}")
                    return []
            else:
                print("Tavily库不可用")
                return []

        try:
            print(f"正在调用Tavily API搜索: {keywords}")
            # 调用Tavily搜索API
            response = self.search_client.search(
                query=keywords,
                search_depth="basic",  # 可以是 "basic" 或 "advanced"
                max_results=max_results,
                include_answer=False,
                include_raw_content=False,
                include_images=False
            )

            print(f"Tavily API响应类型: {type(response)}")
            if isinstance(response, dict):
                print(f"响应键: {list(response.keys())}")

            # 标准化结果格式
            results = []
            if isinstance(response, dict) and 'results' in response:
                for r in response['results']:
                    results.append({
                        'title': r.get('title', '无标题'),
                        'url': r.get('url', '无链接'),
                        'description': r.get('content', '无摘要')
                    })
                print(f"Tavily搜索成功，返回 {len(results)} 个结果")
            else:
                print(f"Tavily响应格式异常: {response}")

            return results

        except Exception as e:
            print(f"Tavily搜索API调用失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def save_results_to_json(self, results: List[Dict[str, Any]], filename: str):
        """
        将搜索结果保存为JSON文件

        Args:
            results: 搜索结果列表
            filename: 保存的文件名
        """
        if not results:
            print(f"没有可保存的结果")
            return

        # 创建保存目录（只有当filename包含路径时才创建目录）
        dirname = os.path.dirname(filename)
        if dirname:  # 只有当dirname不为空时才创建目录
            os.makedirs(dirname, exist_ok=True)

        # 保存为JSON文件
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"搜索结果已保存到 {filename}")


if __name__ == "__main__":
    # 测试代码
    print("测试搜索引擎封装...")

    # 测试 DuckDuckGo
    # print("\n=== 测试 DuckDuckGo ===")
    # ddg_engine = SearchEngine(engine="ddg")
    # ddg_results = ddg_engine.search("商汤科技", max_results=2)
    # for i, result in enumerate(ddg_results, 1):
    #     print(f"{i}. {result['title']}")
    #     print(f"   URL: {result['url']}")
    #     print(f"   描述: {result['description'][:100]}...")

    # # 测试搜狗（如果可用）
    # if SOGOU_AVAILABLE:
    #     print("\n=== 测试搜狗搜索 ===")
    #     sogou_engine = SearchEngine(engine="sogou")
    #     sogou_results = sogou_engine.search("商汤科技", max_results=2)
    #     for i, result in enumerate(sogou_results, 1):
    #         print(f"{i}. {result['title']}")
    #         print(f"   URL: {result['url']}")
    #         print(f"   描述: {result['description'][:100]}...")
    # else:
    #     print("\n=== 搜狗搜索不可用 (k_sogou_search 未安装) ===")

    # # 测试Tavily（如果可用）
    # if TAVILY_AVAILABLE:
    #     print("\n=== 测试 Tavily 搜索 ===")
    #     tavily_engine = SearchEngine(engine="tavily")
    #     tavily_results = tavily_engine.search("商汤科技", max_results=2)
    #     for i, result in enumerate(tavily_results, 1):
    #         print(f"{i}. {result['title']}")
    #         print(f"   URL: {result['url']}")
    #         print(f"   描述: {result['description'][:100]}...")
    # else:
    #     print("\n=== Tavily搜索不可用 (tavily-python 未安装) ===")
    
    # 测试搜索并保存为JSON
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"search_results_{timestamp}.json"
    
    target_results = SearchEngine("tavily").search(" 商汤科技 行业地位 市场份额 竞争分析 业务模式", 10, save_to_json=filename)
    for i, result in enumerate(target_results, 1):
        print(f"{i}. {result['title']}")
        print(f"   URL: {result['url']}")
        print(f"   描述: {result['description'][:100]}...")
