#!/usr/bin/env python3
"""
修复深度研报中的图片引用，只保留实际存在的图片
"""

import re
import os
import glob

def get_available_images():
    """获取实际存在的图片列表"""
    available_images = []
    
    # 检查images目录下的所有png文件
    image_patterns = [
        "./images/*.png",
        "./*.png"
    ]
    
    for pattern in image_patterns:
        available_images.extend(glob.glob(pattern))
    
    # 提取文件名（不含路径）
    image_names = [os.path.basename(img) for img in available_images]
    return image_names

def fix_image_references(md_file_path):
    """修复markdown文件中的图片引用"""
    # 获取实际存在的图片
    available_images = get_available_images()
    print(f"发现 {len(available_images)} 个实际存在的图片文件:")
    for img in available_images:
        print(f"  - {img}")
    
    # 读取markdown文件
    with open(md_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有图片引用
    image_pattern = re.compile(r'!\[([^\]]*)\]\(([^)]+)\)')
    matches = image_pattern.findall(content)
    
    print(f"\n在报告中发现 {len(matches)} 个图片引用:")
    
    # 统计引用情况
    valid_refs = []
    invalid_refs = []
    
    for alt_text, img_path in matches:
        # 提取文件名
        img_filename = os.path.basename(img_path)
        # 去掉URL编码
        import urllib.parse
        img_filename = urllib.parse.unquote(img_filename)
        
        if img_filename in available_images:
            valid_refs.append((alt_text, img_path, img_filename))
            print(f"  ✅ 有效: {alt_text} -> {img_filename}")
        else:
            invalid_refs.append((alt_text, img_path, img_filename))
            print(f"  ❌ 无效: {alt_text} -> {img_filename}")
    
    print(f"\n统计结果:")
    print(f"  有效图片引用: {len(valid_refs)}")
    print(f"  无效图片引用: {len(invalid_refs)}")
    
    # 移除无效的图片引用
    if invalid_refs:
        print(f"\n正在移除 {len(invalid_refs)} 个无效图片引用...")

        # 使用更通用的方法移除所有无效图片引用
        def replace_invalid_image(match):
            alt_text = match.group(1)
            img_path = match.group(2)
            img_filename = os.path.basename(img_path)
            # 去掉URL编码
            img_filename = urllib.parse.unquote(img_filename)

            if img_filename in available_images:
                # 保留有效的图片引用
                return match.group(0)
            else:
                # 移除无效的图片引用
                return f"<!-- 图片不存在: {img_filename} -->"

        content = image_pattern.sub(replace_invalid_image, content)
    
    # 保存修复后的文件
    fixed_file_path = md_file_path.replace('.md', '_fixed.md')
    with open(fixed_file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"\n修复完成！")
    print(f"原文件: {md_file_path}")
    print(f"修复后文件: {fixed_file_path}")
    
    return fixed_file_path

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python fix_images.py <markdown文件路径>")
        sys.exit(1)
    
    md_file = sys.argv[1]
    if not os.path.exists(md_file):
        print(f"错误: 文件 {md_file} 不存在")
        sys.exit(1)
    
    fixed_file = fix_image_references(md_file)
    print(f"\n✅ 图片引用修复完成: {fixed_file}")
