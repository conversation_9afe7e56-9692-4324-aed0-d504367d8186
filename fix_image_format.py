#!/usr/bin/env python3
"""
修复深度研报中的图片引用格式，将 [图片描述: 路径] 转换为 ![图片描述](路径)
"""

import re
import sys

def fix_image_format(md_file_path):
    """修复markdown文件中的图片引用格式"""
    
    # 读取markdown文件
    with open(md_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找所有 [图片描述: 路径] 格式的引用，特别是包含images的
    pattern = re.compile(r'\[([^:\]]+):\s*([^\]]*images[^\]]*)\]')
    matches = pattern.findall(content)
    
    print(f"在报告中发现 {len(matches)} 个需要修复的图片引用:")
    
    # 替换为标准markdown格式
    for i, (alt_text, img_path) in enumerate(matches, 1):
        old_format = f"[{alt_text}: {img_path}]"
        new_format = f"![{alt_text}]({img_path})"
        content = content.replace(old_format, new_format)
        print(f"{i}. {alt_text} -> 已修复")
    
    # 保存修复后的文件
    fixed_file_path = md_file_path.replace('.md', '_图片修复版.md')
    with open(fixed_file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"\n修复完成！")
    print(f"原文件: {md_file_path}")
    print(f"修复后文件: {fixed_file_path}")
    
    return fixed_file_path

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python fix_image_format.py <markdown文件路径>")
        sys.exit(1)
    
    md_file = sys.argv[1]
    fixed_file = fix_image_format(md_file)
    print(f"\n✅ 图片格式修复完成: {fixed_file}")
