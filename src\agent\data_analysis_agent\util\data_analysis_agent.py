# -*- coding: utf-8 -*-
"""
基于LangGraph框架的数据分析智能体
包含数据清洗、分析、可视化和报告生成的完整流程
支持supervisor监督和多节点协作
"""

import os
import json
import yaml
import re
from typing import Dict, Any, List, Optional, Literal, Annotated
from pydantic import BaseModel, Field

# LangGraph相关导入
from langgraph.graph import StateGraph, START, END
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage

# 现有工具导入
from ..tool.create_session_dir import create_session_output_dir
from ..tool.code_executor import CodeExecutor
from ..tool.format_execution_result import format_execution_result
from ..tool.extract_code import extract_code_from_response
from ..tool.llm_helper import LLMHelper
from ..config.llm_config import LLMConfig
from .prompts import (
    data_analysis_system_prompt,
    data_cleaning_system_prompt,
    data_visualization_system_prompt,
    report_generation_system_prompt,
    final_report_system_prompt,
    final_report_system_prompt_absolute
)


# ================== 数据分析师状态类 ==================
class DataAnalysisState(BaseModel):
    """数据分析流程状态管理"""
    messages: Annotated[list, Field(default_factory=list)]
    next_agent: Literal["data_cleaner", "data_analyzer", "chart_analysis_collector", "report_generator", "supervisor", "done"] = "data_cleaner"
    step_count: int = 0
    is_finalized: bool = False
    meta_data: list = Field(default_factory=list)  # 数据存储

    # 分析过程数据
    session_output_dir: str = ""
    analysis_results: list = Field(default_factory=list)
    collected_figures: list = Field(default_factory=list)
    user_input: str = ""
    files: list = Field(default_factory=list)

    # 错误重试相关
    retry_count: int = 0
    max_retries: int = 3

    @classmethod
    def from_raw(cls, raw):
        """统一状态转换方法"""
        if isinstance(raw, cls):
            return raw
        if isinstance(raw, dict):
            # 确保next_agent值有效
            next_agent = raw.get('next_agent', 'data_cleaner')
            valid_agents = {"data_cleaner", "data_analyzer", "chart_analysis_collector", "report_generator", "supervisor", "done"}
            if next_agent not in valid_agents:
                next_agent = 'data_cleaner'
            return cls(
                messages=raw.get('messages', []),
                next_agent=next_agent,
                step_count=raw.get('step_count', 0),
                is_finalized=raw.get('is_finalized', False),
                meta_data=raw.get('meta_data', []),
                session_output_dir=raw.get('session_output_dir', ''),
                analysis_results=raw.get('analysis_results', []),
                collected_figures=raw.get('collected_figures', []),
                user_input=raw.get('user_input', ''),
                files=raw.get('files', []),
                retry_count=raw.get('retry_count', 0),
                max_retries=raw.get('max_retries', 3)
            )
        raise ValueError(f"无效状态类型: {type(raw)}")

    def to_safe_dict(self):
        """安全转换为字典"""
        return {
            "messages": [msg.model_dump() if hasattr(msg, 'model_dump') else msg.dict() for msg in self.messages],
            "next_agent": self.next_agent,
            "step_count": self.step_count,
            "is_finalized": self.is_finalized,
            "meta_data": self.meta_data,
            "session_output_dir": self.session_output_dir,
            "analysis_results": self.analysis_results,
            "collected_figures": self.collected_figures,
            "user_input": self.user_input,
            "files": self.files
        }


# ================== 辅助函数 ==================
def convert_messages(raw_messages):
    """转换消息格式为LangChain格式"""
    converted = []
    for msg in raw_messages:
        if isinstance(msg, dict):
            try:
                msg_type = {
                    "user": HumanMessage,
                    "system": SystemMessage,
                    "assistant": AIMessage
                }[msg["role"]]
                converted.append(msg_type(content=msg.get("content", "")))
            except KeyError:
                converted.append(AIMessage(content=str(msg)))
        elif hasattr(msg, 'content'):
            converted.append(msg)
        else:
            converted.append(AIMessage(content=str(msg)))
    return converted


# ================== 数据清洗节点 ==================
def create_data_cleaner(llm_config: LLMConfig = None):
    """创建数据清洗节点"""
    config = llm_config or LLMConfig()
    llm = LLMHelper(config)

    def data_cleaner(raw_state):
        state = DataAnalysisState.from_raw(raw_state)
        if state.is_finalized:
            return state

        try:
            print(f"[数据清洗] 开始处理用户需求: {state.user_input[:50]}...")

            # 初始化会话目录和执行器
            if not state.session_output_dir:
                session_dir = create_session_output_dir("outputs", state.user_input)
                executor = CodeExecutor(session_dir)
                executor.set_variable('session_output_dir', session_dir)

                # 更新状态
                state.session_output_dir = session_dir
                state.meta_data.append({
                    'type': 'session_info',
                    'session_dir': session_dir,
                    'executor_initialized': True
                })
            else:
                executor = CodeExecutor(state.session_output_dir)
                executor.set_variable('session_output_dir', state.session_output_dir)

            # 设置文件路径环境变量
            if state.files:
                # 设置所有文件的路径
                if len(state.files) == 1:
                    # 单个文件时保持向后兼容
                    file_path = state.files[0]
                    executor.set_variable('file_path', file_path)
                    print(f"[数据清洗] 设置文件路径: {file_path}")
                else:
                    # 多个文件时设置文件列表
                    executor.set_variable('file_paths', state.files)
                    executor.set_variable('file_path', state.files[0])  # 保持向后兼容
                    print(f"[数据清洗] 设置多个文件路径: {', '.join(state.files)}")

                executor.set_variable('encoding', 'utf-8')  # 默认编码

            # 构建数据清洗专用提示词
            notebook_variables = executor.get_environment_info()
            formatted_system_prompt = data_cleaning_system_prompt.format(
                notebook_variables=notebook_variables
            )

            # 构建用户消息（仅在第一次进入该阶段时添加）
            initial_prompt = f"用户需求: {state.user_input}"
            if state.files:
                if len(state.files) == 1:
                    initial_prompt += f"\n数据文件: {state.files[0]}"
                else:
                    initial_prompt += f"\n数据文件: {', '.join(state.files)}"
                    initial_prompt += f"\n注意: 需要处理所有 {len(state.files)} 个文件，可以通过 file_paths 变量访问所有文件路径列表"
            initial_prompt += "\n\n请开始数据探索和清洗阶段，首先加载数据并进行初步探索。"

            messages = convert_messages(state.messages)

            # 检查是否已经有初始提示，避免重复添加
            has_initial_prompt = False
            for msg in messages:
                if isinstance(msg, HumanMessage) and ("用户需求:" in msg.content or "数据探索和清洗阶段" in msg.content):
                    has_initial_prompt = True
                    break

            if not has_initial_prompt:
                messages.append(HumanMessage(content=initial_prompt))

            # 调用LLM进行数据清洗
            response = llm.call(
                prompt="\n\n".join([msg.content for msg in messages]),
                system_prompt=formatted_system_prompt
            )

            print(f"[数据清洗] LLM响应: {response[:200]}...")

            # 处理响应并执行代码
            process_result = _process_llm_response(response, executor, state)

            # 更新状态
            new_messages = state.messages + [AIMessage(content=response)]
            if process_result.get('feedback'):
                new_messages.append(HumanMessage(content=f"执行反馈: {process_result['feedback']}"))

            # 检查是否需要重试（代码执行失败）
            if process_result.get('need_retry') and not process_result.get('execution_success', True):
                # 检查重试次数
                if state.retry_count >= state.max_retries:
                    print(f"❌ 数据清洗重试次数已达上限({state.max_retries})，跳转到supervisor")
                    error_msg = f"数据清洗阶段重试{state.max_retries}次后仍然失败，请检查数据或代码逻辑"
                    new_messages.append(HumanMessage(content=error_msg))
                    return DataAnalysisState(
                        messages=new_messages,
                        next_agent="supervisor",
                        step_count=state.step_count + 1,
                        is_finalized=False,
                        meta_data=state.meta_data,
                        session_output_dir=state.session_output_dir,
                        analysis_results=state.analysis_results,
                        collected_figures=state.collected_figures,
                        user_input=state.user_input,
                        files=state.files,
                        retry_count=0,  # 重置重试计数
                        max_retries=state.max_retries
                    )

                error_msg = process_result.get('error_message', '代码执行失败')
                retry_prompt = f"上一次代码执行失败，错误信息：{error_msg}。请修正代码并重新执行。这是第{state.retry_count + 1}次重试。"
                new_messages.append(HumanMessage(content=retry_prompt))

                print(f"🔄 数据清洗代码执行失败，第{state.retry_count + 1}次重试: {error_msg}")

                # 继续在当前节点重试
                return DataAnalysisState(
                    messages=new_messages,
                    next_agent="data_cleaner",  # 继续当前节点
                    step_count=state.step_count + 1,
                    is_finalized=False,
                    meta_data=state.meta_data,
                    session_output_dir=state.session_output_dir,
                    analysis_results=state.analysis_results,
                    collected_figures=state.collected_figures,
                    user_input=state.user_input,
                    files=state.files,
                    retry_count=state.retry_count + 1,
                    max_retries=state.max_retries
                )

            # 记录分析结果（仅在执行成功时）
            if process_result.get('code') and process_result.get('execution_success', True):
                state.analysis_results.append({
                    'stage': 'data_cleaning',
                    'round': state.step_count + 1,
                    'code': process_result['code'],
                    'result': process_result.get('result', {}),
                    'response': response
                })

            # 决定下一步
            next_agent = "supervisor"

            # 检查是否应该继续当前节点
            if process_result.get('should_continue_current_node'):
                next_agent = "data_cleaner"  # 继续当前节点
                print("🔄 根据next_steps指令，继续当前数据清洗节点")
            elif process_result.get('action') == 'analysis_complete' or process_result.get('should_complete'):
                next_agent = "supervisor"  # 让supervisor决定下一步
                print("✅ 数据清洗完成，交给supervisor决定下一步")
            elif "数据清洗完成" in response or "清洗阶段完成" in response:
                next_agent = "supervisor"

            return DataAnalysisState(
                messages=new_messages,
                next_agent=next_agent,
                step_count=state.step_count + 1,
                is_finalized=process_result.get('action') == 'analysis_complete',
                meta_data=state.meta_data,
                session_output_dir=state.session_output_dir,
                analysis_results=state.analysis_results,
                collected_figures=state.collected_figures,
                user_input=state.user_input,
                files=state.files,
                retry_count=0,  # 成功执行后重置重试计数
                max_retries=state.max_retries
            )

        except Exception as e:
            print(f"[数据清洗错误] {str(e)}")
            return DataAnalysisState(
                messages=state.messages + [AIMessage(content=f"数据清洗过程中发生错误: {str(e)}")],
                next_agent="supervisor",
                step_count=state.step_count + 1,
                is_finalized=False,
                meta_data=state.meta_data,
                session_output_dir=state.session_output_dir,
                analysis_results=state.analysis_results,
                collected_figures=state.collected_figures,
                user_input=state.user_input,
                files=state.files,
                retry_count=state.retry_count,
                max_retries=state.max_retries
            )

    return data_cleaner


# ================== 辅助函数 ==================
def create_updated_state(state: DataAnalysisState, **updates) -> DataAnalysisState:
    """创建更新后的状态，自动处理retry_count和max_retries字段"""
    default_updates = {
        'retry_count': getattr(state, 'retry_count', 0),
        'max_retries': getattr(state, 'max_retries', 3)
    }
    default_updates.update(updates)

    return DataAnalysisState(
        messages=default_updates.get('messages', state.messages),
        next_agent=default_updates.get('next_agent', state.next_agent),
        step_count=default_updates.get('step_count', state.step_count),
        is_finalized=default_updates.get('is_finalized', state.is_finalized),
        meta_data=default_updates.get('meta_data', state.meta_data),
        session_output_dir=default_updates.get('session_output_dir', state.session_output_dir),
        analysis_results=default_updates.get('analysis_results', state.analysis_results),
        collected_figures=default_updates.get('collected_figures', state.collected_figures),
        user_input=default_updates.get('user_input', state.user_input),
        files=default_updates.get('files', state.files),
        retry_count=default_updates.get('retry_count', 0),
        max_retries=default_updates.get('max_retries', 3)
    )


# ================== 响应处理函数 ==================
def _process_llm_response(response: str, executor: CodeExecutor, state: DataAnalysisState) -> Dict[str, Any]:
    """处理LLM响应，执行代码并返回结果"""
    try:
        # 使用LLMHelper解析YAML响应
        temp_llm = LLMHelper(LLMConfig())
        yaml_data = temp_llm.parse_yaml_response(response)

        action = yaml_data.get('action', 'generate_code')
        next_steps = yaml_data.get('next_steps', [])

        # 解析next_steps，判断是否需要继续当前节点
        should_continue_current_node = False
        should_complete = False

        if next_steps:
            next_steps_text = ' '.join(next_steps) if isinstance(next_steps, list) else str(next_steps)
            next_steps_lower = next_steps_text.lower()

            # 检查是否包含"继续"相关关键词
            continue_keywords = ['继续', 'continue', '再次', '重新', '进一步', '深入']
            complete_keywords = ['完成', 'complete', '结束', 'done', '生成报告', '准备']

            if any(keyword in next_steps_lower for keyword in continue_keywords):
                should_continue_current_node = True
                print(f"🔄 检测到继续指令: {next_steps_text}")
            elif any(keyword in next_steps_lower for keyword in complete_keywords):
                should_complete = True
                print(f"✅ 检测到完成指令: {next_steps_text}")

        if action == 'analysis_complete':
            return {
                'action': 'analysis_complete',
                'response': response,
                'next_steps': next_steps,
                'should_continue_current_node': False,
                'should_complete': True
            }
        elif action == 'collect_figures':
            result = _handle_collect_figures(response, state)
            result.update({
                'next_steps': next_steps,
                'should_continue_current_node': should_continue_current_node,
                'should_complete': should_complete
            })
            return result
        else:
            # 默认处理代码生成
            result = _handle_generate_code(response, executor, state.session_output_dir)
            result.update({
                'next_steps': next_steps,
                'should_continue_current_node': should_continue_current_node,
                'should_complete': should_complete
            })
            return result

    except Exception as e:
        print(f"⚠️ 解析响应失败: {str(e)}，按generate_code处理")
        result = _handle_generate_code(response, executor, state.session_output_dir)
        result.update({
            'next_steps': [],
            'should_continue_current_node': False,
            'should_complete': False
        })
        return result


def _handle_generate_code(response: str, executor: CodeExecutor, session_output_dir: str = None) -> Dict[str, Any]:
    """处理代码生成和执行"""
    code = extract_code_from_response(response)
    if code:
        print(f"🔧 执行代码:\n{code}")
        print("-" * 40)
        result = executor.execute_code(code)
        feedback = format_execution_result(result)
        print(f"📋 执行反馈:\n{feedback}")

        # 检查代码执行是否成功
        execution_success = result.get('success', False)
        error_msg = result.get('error', '')
        has_error = bool(error_msg and error_msg.strip())  # 只有非空的错误信息才算有错误

        # 如果执行失败，需要重试
        if not execution_success or has_error:
            print(f"❌ 代码执行失败: {error_msg}")
            return {
                'action': 'generate_code',
                'code': code,
                'result': result,
                'feedback': feedback,
                'response': response,
                'execution_success': False,
                'error_message': error_msg,
                'need_retry': True  # 标记需要重试
            }

        # 检查代码执行结果中是否有图片生成但文件不存在的情况
        missing_figures = []
        output = result.get('output', '')
        # 简单正则或字符串查找图片路径并判断是否存在
        import re
        img_paths = re.findall(r'(?:[\w./\\-]+\.(?:png|jpg|jpeg|svg))', str(output))
        for img_path in img_paths:
            if not os.path.isabs(img_path):
                # 如果没有提供session_output_dir，尝试从executor获取
                if session_output_dir:
                    abs_path = os.path.join(session_output_dir, img_path)
                else:
                    # 尝试从executor的环境变量中获取
                    session_dir = getattr(executor, 'output_dir', None)
                    if session_dir:
                        abs_path = os.path.join(session_dir, img_path)
                    else:
                        abs_path = img_path
            else:
                abs_path = img_path
            if not os.path.exists(abs_path):
                missing_figures.append(img_path)

        if missing_figures:
            feedback += f"\n⚠️ 检测到以下图片未生成成功: {missing_figures}\n建议重新分析本轮或修正代码后再试。"
            return {
                'action': 'generate_code',
                'code': code,
                'result': result,
                'feedback': feedback,
                'response': response,
                'execution_success': False,
                'missing_figures': missing_figures,
                'need_retry': True
            }

        # 执行成功
        return {
            'action': 'generate_code',
            'code': code,
            'result': result,
            'feedback': feedback,
            'response': response,
            'execution_success': True,
            'need_retry': False
        }
    else:
        print("⚠️ 未从响应中提取到可执行代码")
        return {
            'action': 'invalid_response',
            'error': '响应中缺少可执行代码',
            'response': response,
            'execution_success': False,
            'need_retry': True
        }


def _handle_collect_figures(response: str, state: DataAnalysisState) -> Dict[str, Any]:
    """处理图片收集"""
    print("📊 开始收集图片")

    # 解析YAML响应以获取图片信息
    try:
        # 创建临时LLMHelper实例来解析YAML
        temp_llm = LLMHelper(LLMConfig())
        yaml_data = temp_llm.parse_yaml_response(response)
        figures_to_collect = yaml_data.get('figures_to_collect', [])
    except Exception as e:
        print(f"⚠️ 解析YAML响应失败: {str(e)}")
        figures_to_collect = []

    collected_figures = []

    for figure_info in figures_to_collect:
        figure_number = figure_info.get('figure_number')
        filename = figure_info.get('filename', f'figure_{figure_number}.png')
        file_path = figure_info.get('file_path', '')  # 获取具体的文件路径
        description = figure_info.get('description', '')
        analysis = figure_info.get('analysis', '')

        print(f"📈 收集图片 {figure_number}: {filename}")
        print(f"   📂 路径: {file_path}")
        print(f"   📝 描述: {description}")
        print(f"   🔍 分析: {analysis}")

        # 规范化文件路径，处理Windows路径问题
        if file_path:
            # 将路径规范化为当前操作系统的格式
            normalized_path = os.path.normpath(file_path)
            print(f"   🔧 规范化路径: {normalized_path}")

            # 检查文件是否存在
            if os.path.exists(normalized_path):
                print(f"   ✅ 文件存在: {normalized_path}")
                collected_figures.append({
                    'figure_number': figure_number,
                    'filename': filename,
                    'file_path': normalized_path,
                    'description': description,
                    'analysis': analysis
                })
            else:
                print(f"   ⚠️ 文件不存在: {normalized_path}，已经跳过")
                
        else:
            print(f"   ⚠️ 未提供文件路径，已跳过该图片")

    return {
        'action': 'collect_figures',
        'collected_figures': collected_figures,
        'response': response,  # response 仍然原样保留，若需彻底净化可进一步处理
        'continue': True
    }


# ================== 数据分析节点 ==================
def create_data_analyzer(llm_config: LLMConfig = None):
    """创建数据分析节点"""
    config = llm_config or LLMConfig()
    llm = LLMHelper(config)

    def data_analyzer(raw_state):
        state = DataAnalysisState.from_raw(raw_state)
        if state.is_finalized:
            return state

        try:
            print(f"[数据分析] 开始统计分析阶段...")

            executor = CodeExecutor(state.session_output_dir)
            executor.set_variable('session_output_dir', state.session_output_dir)

            # 设置文件路径环境变量
            if state.files:
                # 设置所有文件的路径
                if len(state.files) == 1:
                    # 单个文件时保持向后兼容
                    file_path = state.files[0]
                    executor.set_variable('file_path', file_path)
                    print(f"[数据分析] 设置文件路径: {file_path}")
                else:
                    # 多个文件时设置文件列表
                    executor.set_variable('file_paths', state.files)
                    executor.set_variable('file_path', state.files[0])  # 保持向后兼容
                    print(f"[数据分析] 设置多个文件路径: {', '.join(state.files)}")

                executor.set_variable('encoding', 'utf-8')

            # 构建数据分析专用提示词
            notebook_variables = executor.get_environment_info()
            formatted_system_prompt = data_analysis_system_prompt.format(
                notebook_variables=notebook_variables
            )

            # 添加分析阶段的特定指导（仅在第一次进入该阶段时添加）
            analysis_prompt = "现在进入数据分析阶段，请对已清洗的数据进行统计分析，计算关键指标和特征。"

            messages = convert_messages(state.messages)

            # 检查是否已经有分析阶段的提示，避免重复添加
            has_analysis_prompt = False
            for msg in messages:
                if isinstance(msg, HumanMessage) and "数据分析阶段" in msg.content:
                    has_analysis_prompt = True
                    break

            if not has_analysis_prompt:
                messages.append(HumanMessage(content=analysis_prompt))

            # 调用LLM进行数据分析
            response = llm.call(
                prompt="\n\n".join([msg.content for msg in messages]),
                system_prompt=formatted_system_prompt
            )

            print(f"[数据分析] LLM响应: {response[:200]}...")

            # 处理响应并执行代码
            process_result = _process_llm_response(response, executor, state)

            # 更新状态
            new_messages = state.messages + [AIMessage(content=response)]
            if process_result.get('feedback'):
                new_messages.append(HumanMessage(content=f"执行反馈: {process_result['feedback']}"))

            # 检查是否需要重试（代码执行失败）
            if process_result.get('need_retry') and not process_result.get('execution_success', True):
                # 检查重试次数
                if state.retry_count >= state.max_retries:
                    print(f"❌ 数据分析重试次数已达上限({state.max_retries})，跳转到supervisor")
                    error_msg = f"数据分析阶段重试{state.max_retries}次后仍然失败，请检查数据或代码逻辑"
                    new_messages.append(HumanMessage(content=error_msg))
                    return create_updated_state(state,
                        messages=new_messages,
                        next_agent="supervisor",
                        step_count=state.step_count + 1,
                        retry_count=0  # 重置重试计数
                    )

                error_msg = process_result.get('error_message', '代码执行失败')
                retry_prompt = f"上一次代码执行失败，错误信息：{error_msg}。请修正代码并重新执行。这是第{state.retry_count + 1}次重试。"
                new_messages.append(HumanMessage(content=retry_prompt))

                print(f"🔄 数据分析代码执行失败，第{state.retry_count + 1}次重试: {error_msg}")

                # 继续在当前节点重试
                return create_updated_state(state,
                    messages=new_messages,
                    next_agent="data_analyzer",  # 继续当前节点
                    step_count=state.step_count + 1,
                    retry_count=state.retry_count + 1
                )

            # 记录分析结果（仅在执行成功时）
            if process_result.get('code') and process_result.get('execution_success', True):
                state.analysis_results.append({
                    'stage': 'data_analysis',
                    'round': state.step_count + 1,
                    'code': process_result['code'],
                    'result': process_result.get('result', {}),
                    'response': response
                })

            # 决定下一步
            next_agent = "supervisor"

            # 检查是否应该继续当前节点
            if process_result.get('should_continue_current_node'):
                next_agent = "data_analyzer"  # 继续当前节点
                print("🔄 根据next_steps指令，继续当前数据分析节点")
            elif process_result.get('action') == 'analysis_complete' or process_result.get('should_complete'):
                next_agent = "supervisor"  # 让supervisor决定下一步
                print("✅ 数据分析完成，交给supervisor决定下一步")
            elif "分析完成" in response or "统计分析完成" in response:
                next_agent = "supervisor"

            return create_updated_state(state,
                messages=new_messages,
                next_agent=next_agent,
                step_count=state.step_count + 1,
                is_finalized=process_result.get('action') == 'analysis_complete',
                retry_count=0  # 成功执行后重置重试计数
            )

        except Exception as e:
            print(f"[数据分析错误] {str(e)}")
            return create_updated_state(state,
                messages=state.messages + [AIMessage(content=f"数据分析过程中发生错误: {str(e)}")],
                next_agent="supervisor",
                step_count=state.step_count + 1,
                is_finalized=False
            )

    return data_analyzer


# ================== 图表分析收集节点 ==================
def create_chart_analysis_collector(llm_config: LLMConfig = None):
    """创建图表分析收集节点 - 专注于对图表内容的深度分析和专业解读"""
    config = llm_config or LLMConfig()
    llm = LLMHelper(config)

    def chart_analysis_collector(raw_state):
        state = DataAnalysisState.from_raw(raw_state)
        if state.is_finalized:
            return state

        try:
            print(f"[图表分析收集] 开始深度分析已生成的图表...")

            executor = CodeExecutor(state.session_output_dir)
            executor.set_variable('session_output_dir', state.session_output_dir)

            # 设置文件路径环境变量
            if state.files:
                # 设置所有文件的路径
                if len(state.files) == 1:
                    # 单个文件时保持向后兼容
                    file_path = state.files[0]
                    executor.set_variable('file_path', file_path)
                    print(f"[图表分析收集] 设置文件路径: {file_path}")
                else:
                    # 多个文件时设置文件列表
                    executor.set_variable('file_paths', state.files)
                    executor.set_variable('file_path', state.files[0])  # 保持向后兼容
                    print(f"[图表分析收集] 设置多个文件路径: {', '.join(state.files)}")

                executor.set_variable('encoding', 'utf-8')

            # 构建图表分析收集专用提示词
            notebook_variables = executor.get_environment_info()
            formatted_system_prompt = data_visualization_system_prompt.format(
                notebook_variables=notebook_variables
            )

            # 获取会话目录中实际存在的图表文件
            actual_chart_files = []
            if state.session_output_dir and os.path.exists(state.session_output_dir):
                for file in os.listdir(state.session_output_dir):
                    if file.endswith('.png'):
                        actual_chart_files.append(file)

            chart_files_info = ""
            if actual_chart_files:
                chart_files_info = f"\n\n📊 会话目录中实际存在的图表文件：\n"
                for i, filename in enumerate(actual_chart_files, 1):
                    chart_files_info += f"{i}. {filename}\n"
                chart_files_info += "\n⚠️ 重要：请只收集上述实际存在的图表文件，使用准确的文件名。"

            # 添加图表分析收集阶段的特定指导（仅在第一次进入该阶段时添加）
            analysis_prompt = f"""现在进入图表分析收集阶段，你的主要任务是：
1. 识别和收集会话目录中所有已生成的图表文件
2. 对每个图表进行专业的深度分析和解读
3. 提取图表中的关键业务洞察和数据趋势
4. 为最终报告准备高质量的图表分析内容

请专注于图表内容的深度分析和专业解读，而不是生成新的图表。{chart_files_info}"""

            messages = convert_messages(state.messages)

            # 检查是否已经有图表分析收集阶段的提示，避免重复添加
            has_analysis_prompt = False
            for msg in messages:
                if isinstance(msg, HumanMessage) and ("图表分析收集阶段" in msg.content or "图标收集阶段" in msg.content):
                    has_analysis_prompt = True
                    break

            if not has_analysis_prompt:
                messages.append(HumanMessage(content=analysis_prompt))

            # 调用LLM进行图表分析收集
            response = llm.call(
                prompt="\n\n".join([msg.content for msg in messages]),
                system_prompt=formatted_system_prompt
            )

            print(f"[图表分析收集] LLM响应: {response[:200]}...")

            # 处理响应并执行代码
            process_result = _process_llm_response(response, executor, state)

            # 更新状态
            new_messages = state.messages + [AIMessage(content=response)]
            if process_result.get('feedback'):
                new_messages.append(HumanMessage(content=f"执行反馈: {process_result['feedback']}"))

            # 检查是否需要重试（代码执行失败）
            if process_result.get('need_retry') and not process_result.get('execution_success', True):
                error_msg = process_result.get('error_message', '代码执行失败')
                retry_prompt = f"上一次代码执行失败，错误信息：{error_msg}。请修正代码并重新执行。"
                new_messages.append(HumanMessage(content=retry_prompt))

                print(f"🔄 图表分析收集阶段代码执行失败，需要重试: {error_msg}")

                # 继续在当前节点重试
                return DataAnalysisState(
                    messages=new_messages,
                    next_agent="chart_analysis_collector",  # 继续当前节点
                    step_count=state.step_count + 1,
                    is_finalized=False,
                    meta_data=state.meta_data,
                    session_output_dir=state.session_output_dir,
                    analysis_results=state.analysis_results,
                    collected_figures=state.collected_figures,
                    user_input=state.user_input,
                    files=state.files
                )

            # 记录分析结果（仅在执行成功时）
            if process_result.get('code') and process_result.get('execution_success', True):
                state.analysis_results.append({
                    'stage': 'chart_analysis_collection',
                    'round': state.step_count + 1,
                    'code': process_result['code'],
                    'result': process_result.get('result', {}),
                    'response': response
                })

            # 收集和分析图片 - 这是本节点的核心功能
            if process_result.get('action') == 'collect_figures':
                collected_figures = process_result.get('collected_figures', [])
                state.collected_figures.extend(collected_figures)
                print(f"📊 成功收集并分析了 {len(collected_figures)} 个图表")

                # 输出收集到的图表分析摘要
                for figure in collected_figures:
                    print(f"   📈 {figure.get('filename', '未知文件')}: {figure.get('description', '无描述')[:50]}...")

            # 决定下一步
            next_agent = "supervisor"

            # 检查是否应该继续当前节点
            if process_result.get('should_continue_current_node'):
                next_agent = "chart_analysis_collector"  # 继续当前节点
                print("🔄 根据next_steps指令，继续当前图表分析收集节点")
            elif process_result.get('action') == 'analysis_complete' or process_result.get('should_complete'):
                next_agent = "supervisor"  # 让supervisor决定下一步
                print("✅ 图表分析收集完成，交给supervisor决定下一步")
            elif "图表分析完成" in response or "收集完成" in response or "分析收集完成" in response:
                next_agent = "supervisor"

            return DataAnalysisState(
                messages=new_messages,
                next_agent=next_agent,
                step_count=state.step_count + 1,
                is_finalized=process_result.get('action') == 'analysis_complete',
                meta_data=state.meta_data,
                session_output_dir=state.session_output_dir,
                analysis_results=state.analysis_results,
                collected_figures=state.collected_figures,
                user_input=state.user_input,
                files=state.files
            )

        except Exception as e:
            print(f"[图表分析收集错误] {str(e)}")
            return DataAnalysisState(
                messages=state.messages + [AIMessage(content=f"图表分析收集过程中发生错误: {str(e)}")],
                next_agent="supervisor",
                step_count=state.step_count + 1,
                is_finalized=False,
                meta_data=state.meta_data,
                session_output_dir=state.session_output_dir,
                analysis_results=state.analysis_results,
                collected_figures=state.collected_figures,
                user_input=state.user_input,
                files=state.files
            )

    return chart_analysis_collector


# ================== 报告生成节点 ==================
def create_report_generator(llm_config: LLMConfig = None):
    """创建报告生成节点"""
    config = llm_config or LLMConfig()
    llm = LLMHelper(config)

    def report_generator(raw_state):
        state = DataAnalysisState.from_raw(raw_state)
        if state.is_finalized:
            return state

        try:
            print(f"[报告生成] 开始生成最终分析报告...")

            # 构建最终报告提示词
            figures_summary = ""
            if state.collected_figures:
                figures_summary = "\n生成的图片及分析:\n"
                for i, figure in enumerate(state.collected_figures, 1):
                    filename = figure.get('filename', '未知文件名')
                    figures_summary += f"{i}. {filename}\n"
                    figures_summary += f"   描述: {figure.get('description', '无描述')}\n"
                    figures_summary += f"   分析: {figure.get('analysis', '无分析')}\n\n"

            # 构建代码执行结果摘要
            code_results_summary = ""
            for i, result in enumerate(state.analysis_results, 1):
                if result.get('code'):
                    exec_result = result.get('result', {})
                    if exec_result.get('success'):
                        code_results_summary += f"阶段 {result.get('stage', 'unknown')}: 执行成功\n"
                        if exec_result.get('output'):
                            code_results_summary += f"输出: {exec_result.get('output')[:200]}...\n\n"

            # 使用现有的最终报告提示词模板
            final_report_prompt = final_report_system_prompt.format(
                current_round=state.step_count,
                session_output_dir=state.session_output_dir,
                figures_summary=figures_summary,
                execution_summary=code_results_summary
            )

            # 调用LLM生成最终报告
            response = llm.call(
                prompt=final_report_prompt,
                system_prompt=report_generation_system_prompt,
                max_tokens=16384
            )

            print(f"[报告生成] 报告生成完成")

            # 解析最终报告内容
            final_report_content = response
            try:
                # 尝试解析YAML格式的响应
                if "final_report:" in response:
                    lines = response.split('\n')
                    report_lines = []
                    in_report = False
                    for line in lines:
                        if line.strip().startswith('final_report:'):
                            in_report = True
                            continue
                        if in_report:
                            report_lines.append(line)
                    if report_lines:
                        final_report_content = '\n'.join(report_lines)
            except:
                pass

            # 保存最终报告到文件
            report_file_path = os.path.join(state.session_output_dir, "最终分析报告.md")
            try:
                with open(report_file_path, 'w', encoding='utf-8') as f:
                    f.write(final_report_content)
                print(f"📄 最终报告已保存至: {report_file_path}")
            except Exception as e:
                print(f"❌ 保存报告文件失败: {str(e)}")

            return DataAnalysisState(
                messages=state.messages + [AIMessage(content=f"最终分析报告已生成完成。\n\n{final_report_content}")],
                next_agent="done",
                step_count=state.step_count + 1,
                is_finalized=True,
                meta_data=state.meta_data + [{'type': 'final_report', 'file_path': report_file_path, 'content': final_report_content}],
                session_output_dir=state.session_output_dir,
                analysis_results=state.analysis_results,
                collected_figures=state.collected_figures,
                user_input=state.user_input,
                files=state.files
            )

        except Exception as e:
            print(f"[报告生成错误] {str(e)}")
            return DataAnalysisState(
                messages=state.messages + [AIMessage(content=f"报告生成过程中发生错误: {str(e)}")],
                next_agent="done",
                step_count=state.step_count + 1,
                is_finalized=True,
                meta_data=state.meta_data,
                session_output_dir=state.session_output_dir,
                analysis_results=state.analysis_results,
                collected_figures=state.collected_figures,
                user_input=state.user_input,
                files=state.files
            )

    return report_generator


# ================== Supervisor监督节点 ==================
def create_supervisor(llm_config: LLMConfig = None):
    """创建supervisor监督节点"""
    config = llm_config or LLMConfig()
    llm = LLMHelper(config)

    def supervisor(raw_state):
        state = DataAnalysisState.from_raw(raw_state)
        if state.is_finalized:
            return state

        try:
            print(f"\n[Supervisor] 当前步数: {state.step_count}")
            print(f"[Supervisor] 当前阶段: {state.next_agent}")

            # 智能流程控制
            if state.step_count >= 50:
                print("[强制终止] 达到最大步数限制")
                return DataAnalysisState(
                    messages=state.messages,
                    next_agent="report_generator",
                    step_count=state.step_count + 1,
                    is_finalized=False,
                    meta_data=state.meta_data,
                    session_output_dir=state.session_output_dir,
                    analysis_results=state.analysis_results,
                    collected_figures=state.collected_figures,
                    user_input=state.user_input,
                    files=state.files
                )

            # 首先检查节点是否明确要求继续当前阶段
            # 通过检查state.next_agent是否指向具体的工作节点来判断
            current_node_wants_continue = False

            # 如果next_agent指向具体的工作节点（而不是supervisor），说明节点要求继续执行
            if state.next_agent in ["data_cleaner", "data_analyzer", "chart_analysis_collector", "report_generator"]:
                current_node_wants_continue = True
                print(f"🔍 检测到节点要求继续执行: {state.next_agent}")

            # 检查最近的消息中是否有"继续当前节点"的指令（作为补充检查）
            recent_messages = state.messages[-2:] if len(state.messages) >= 2 else state.messages
            for msg in recent_messages:
                if hasattr(msg, 'content'):
                    content = msg.content.lower()
                    if any(keyword in content for keyword in ['继续当前', '继续数据清洗', '继续数据分析', '继续图表分析', '继续收集']):
                        current_node_wants_continue = True
                        print(f"🔍 检测到消息中的继续指令: {msg.content[:100]}...")
                        break

            # 检查最近的消息中是否有执行反馈错误，需要重试
            has_execution_error = False
            for msg in recent_messages:
                if hasattr(msg, 'content') and '执行反馈:' in msg.content:
                    if any(error_keyword in msg.content.lower() for error_keyword in ['error', '错误', 'exception', '失败']):
                        has_execution_error = True
                        print(f"🔍 检测到执行错误: {msg.content[:100]}...")
                        break

            # 智能决策逻辑
            new_agent = "data_cleaner"  # 默认值

            # 如果节点明确要求继续当前阶段，则直接尊重该决策
            if current_node_wants_continue and state.next_agent in ["data_cleaner", "data_analyzer", "chart_analysis_collector", "report_generator"]:
                new_agent = state.next_agent
                print(f"🔄 尊重节点决策，继续执行: {new_agent}")
            elif has_execution_error:
                # 如果有执行错误，继续当前阶段重试
                if not state.session_output_dir or len(state.analysis_results) == 0:
                    new_agent = "data_cleaner"
                elif not any(r.get('stage') == 'data_analysis' for r in state.analysis_results):
                    new_agent = "data_analyzer"
                elif not any(r.get('stage') in ['data_visualization', 'chart_analysis_collection'] for r in state.analysis_results):
                    new_agent = "chart_analysis_collector"
                else:
                    new_agent = "report_generator"
                print(f"🔄 检测到执行错误，重试当前阶段: {new_agent}")
            else:
                # 正常流程决策
                if not state.session_output_dir:
                    new_agent = "data_cleaner"
                elif len(state.analysis_results) == 0:
                    new_agent = "data_cleaner"
                elif not any(r.get('stage') == 'data_analysis' for r in state.analysis_results):
                    new_agent = "data_analyzer"
                elif not any(r.get('stage') in ['data_visualization', 'chart_analysis_collection'] for r in state.analysis_results) and len(state.collected_figures) == 0:
                    new_agent = "chart_analysis_collector"
                elif not any('final_report' in str(m) for m in state.meta_data):
                    new_agent = "report_generator"
                else:
                    new_agent = "done"

            print(f"[Supervisor] 决策: {new_agent}")

            return DataAnalysisState(
                messages=state.messages,
                next_agent=new_agent,
                step_count=state.step_count + 1,
                is_finalized=False,
                meta_data=state.meta_data,
                session_output_dir=state.session_output_dir,
                analysis_results=state.analysis_results,
                collected_figures=state.collected_figures,
                user_input=state.user_input,
                files=state.files
            )

        except Exception as e:
            print(f"[Supervisor错误] {str(e)}")
            return DataAnalysisState(
                messages=state.messages,
                next_agent="report_generator",
                step_count=state.step_count + 1,
                is_finalized=False,
                meta_data=state.meta_data,
                session_output_dir=state.session_output_dir,
                analysis_results=state.analysis_results,
                collected_figures=state.collected_figures,
                user_input=state.user_input,
                files=state.files
            )

    return supervisor


# ================== LangGraph工作流构建 ==================
class DataAnalysisWorkflow:
    """数据分析工作流管理器"""

    def __init__(self, llm_config: LLMConfig = None):
        self.llm_config = llm_config or LLMConfig()
        self.workflow = self._build_workflow()

    def _build_workflow(self):
        """构建LangGraph工作流"""
        # 创建状态图
        builder = StateGraph(DataAnalysisState)

        # 添加节点
        builder.add_node("supervisor", create_supervisor(self.llm_config))
        builder.add_node("data_cleaner", create_data_cleaner(self.llm_config))
        builder.add_node("data_analyzer", create_data_analyzer(self.llm_config))
        builder.add_node("chart_analysis_collector", create_chart_analysis_collector(self.llm_config))
        builder.add_node("report_generator", create_report_generator(self.llm_config))

        # 定义路由函数
        def route_supervisor(state):
            return state.next_agent

        # 添加条件边
        builder.add_conditional_edges(
            "supervisor",
            route_supervisor,
            {
                "data_cleaner": "data_cleaner",
                "data_analyzer": "data_analyzer",
                "chart_analysis_collector": "chart_analysis_collector",
                "report_generator": "report_generator",
                "supervisor": "supervisor",  # 允许supervisor自循环
                "done": END
            }
        )

        # 所有节点完成后返回supervisor
        for agent in ["data_cleaner", "data_analyzer", "chart_analysis_collector", "report_generator"]:
            builder.add_edge(agent, "supervisor")

        # 设置入口点
        builder.set_entry_point("supervisor")

        # 编译工作流，设置更高的递归限制
        return builder.compile(
            checkpointer=None,  # 不使用检查点
            debug=False,        # 关闭调试模式
        )

    def run_analysis(self, user_input: str, files: List[str] = None) -> Dict[str, Any]:
        """运行数据分析流程"""
        try:
            # 初始化状态
            initial_state = DataAnalysisState(
                messages=[HumanMessage(content=user_input)],
                user_input=user_input,
                files=files or []
            )

            print(f"🚀 开始数据分析任务")
            print(f"📝 用户需求: {user_input}")
            if files:
                print(f"📁 数据文件: {', '.join(files)}")
            print("=" * 60)

            final_state = None
            step_count = 0

            # 执行工作流，设置递归限制配置
            config = {"recursion_limit": 40}  # 设置递归限制为100

            for step in self.workflow.stream(initial_state, config=config):
                step_count += 1
                node_name, raw_state = step.popitem()
                current_state = DataAnalysisState.from_raw(raw_state)

                print(f"\n[系统状态] 当前节点: {node_name}")
                print(f"下一步: {current_state.next_agent}")
                print(f"步数: {current_state.step_count}")
                print(f"完成状态: {current_state.is_finalized}")

                final_state = current_state

                if current_state.is_finalized or node_name == "__end__":
                    print("\n====== 流程完成 =====")
                    break

                if step_count > 80:  # 防止无限循环，增加到80步
                    print("\n⚠️ 达到最大执行步数，强制结束")
                    break

            # 提取最终报告内容
            final_report_content = ""
            if final_state and final_state.meta_data:
                for meta in final_state.meta_data:
                    if meta.get('type') == 'final_report' and meta.get('content'):
                        final_report_content = meta['content']
                        break

            # 返回分析结果
            return {
                'session_output_dir': final_state.session_output_dir if final_state else "",
                'total_rounds': final_state.step_count if final_state else 0,
                'analysis_results': final_state.analysis_results if final_state else [],
                'collected_figures': final_state.collected_figures if final_state else [],
                'meta_data': final_state.meta_data if final_state else [],
                'messages': final_state.messages if final_state else [],
                'is_completed': final_state.is_finalized if final_state else False,
                'final_report': final_report_content
            }

        except Exception as e:
            print(f"\n!!! 流程异常终止: {str(e)}")
            return {
                'error': str(e),
                'session_output_dir': "",
                'total_rounds': 0,
                'analysis_results': [],
                'collected_figures': [],
                'meta_data': [],
                'messages': [],
                'is_completed': False,
                'final_report': ""
            }


# class DataAnalysisAgent:
#     """
#     数据分析智能体
    
#     职责：
#     - 接收用户自然语言需求
#     - 生成Python分析代码
#     - 执行代码并收集结果
#     - 基于执行结果继续生成后续分析代码
#     """
#     def __init__(self, llm_config: LLMConfig = None,
#                  output_dir: str = "outputs",
#                  max_rounds: int = 20,
#                  absolute_path: bool = False):
#         """
#         初始化智能体
        
#         Args:
#             config: LLM配置
#             output_dir: 输出目录
#             max_rounds: 最大对话轮数
#         """
#         self.config = llm_config or LLMConfig()

#         # 验证配置（参照 data_analysis.py 的模式）
#         if not self.config.validate():
#             print("⚠️ LLM配置验证失败，请检查配置")

#         self.llm = LLMHelper(self.config)
#         self.base_output_dir = output_dir
#         self.max_rounds = max_rounds
#           # 对话历史和上下文
#         self.conversation_history = []
#         self.analysis_results = []
#         self.current_round = 0
#         self.session_output_dir = None
#         self.executor = None
#         self.absolute_path = absolute_path

#     def _process_response(self, response: str) -> Dict[str, Any]:
#         """
#         统一处理LLM响应，判断行动类型并执行相应操作
        
#         Args:
#             response: LLM的响应内容
            
#         Returns:
#             处理结果字典
#         """
#         try:
#             yaml_data = self.llm.parse_yaml_response(response)
#             action = yaml_data.get('action', 'generate_code')
            
#             print(f"🎯 检测到动作: {action}")
            
#             if action == 'analysis_complete':
#                 return self._handle_analysis_complete(response, yaml_data)
#             elif action == 'collect_figures':
#                 return self._handle_collect_figures(response, yaml_data)
#             elif action == 'generate_code':
#                 return self._handle_generate_code(response, yaml_data)
#             else:
#                 print(f"⚠️ 未知动作类型: {action}，按generate_code处理")
#                 return self._handle_generate_code(response, yaml_data)
                
#         except Exception as e:
#             print(f"⚠️ 解析响应失败: {str(e)}，按generate_code处理")
#             return self._handle_generate_code(response, {})
    
#     def _handle_analysis_complete(self, response: str, yaml_data: Dict[str, Any]) -> Dict[str, Any]:
#         """处理分析完成动作"""
#         print("✅ 分析任务完成")
#         final_report = yaml_data.get('final_report', '分析完成，无最终报告')
#         return {
#             'action': 'analysis_complete',
#             'final_report': final_report,
#             'response': response,
#             'continue': False
#         }
    
#     def _handle_collect_figures(self, response: str, yaml_data: Dict[str, Any]) -> Dict[str, Any]:
#         """处理图片收集动作"""
#         print("📊 开始收集图片")
#         figures_to_collect = yaml_data.get('figures_to_collect', [])
        
#         collected_figures = []
        
#         for figure_info in figures_to_collect:
#             figure_number = figure_info.get('figure_number')
#             filename = figure_info.get('filename', f'figure_{figure_number}.png')
#             file_path = figure_info.get('file_path', '')  # 获取具体的文件路径
#             description = figure_info.get('description', '')
#             analysis = figure_info.get('analysis', '')
            
#             print(f"📈 收集图片 {figure_number}: {filename}")
#             print(f"   📂 路径: {file_path}")
#             print(f"   📝 描述: {description}")
#             print(f"   🔍 分析: {analysis}")
            
#             # 只保留真实存在的图片
#             if file_path and os.path.exists(file_path):
#                 print(f"   ✅ 文件存在: {file_path}")
#                 collected_figures.append({
#                     'figure_number': figure_number,
#                     'filename': filename,
#                     'file_path': file_path,
#                     'description': description,
#                     'analysis': analysis
#                 })
#             elif file_path:
#                 print(f"   ⚠️ 文件不存在: {file_path}，已跳过该图片")
#             else:
#                 print(f"   ⚠️ 未提供文件路径，已跳过该图片")
        
        
#         return {
#             'action': 'collect_figures',
#             'collected_figures': collected_figures,
#             'response': response,  # response 仍然原样保留，若需彻底净化可进一步处理
#             'continue': True
#         }
#     def _handle_generate_code(self, response: str, yaml_data: Dict[str, Any]) -> Dict[str, Any]:
#         """处理代码生成和执行动作"""
#         code = yaml_data.get('code', '')
#         if not code:
#             code = extract_code_from_response(response)
#         if code:
#             print(f"🔧 执行代码:\n{code}")
#             print("-" * 40)
#             result = self.executor.execute_code(code)
#             feedback = format_execution_result(result)
#             print(f"📋 执行反馈:\n{feedback}")
#             # 检查代码执行结果中是否有图片生成但文件不存在的情况
#             # 假设图片保存路径会在 result['output'] 或 result['figures'] 里体现
#             # 如果检测到图片文件不存在，建议用户重新分析
#             missing_figures = []
#             output = result.get('output', '')
#             # 简单正则或字符串查找图片路径并判断是否存在
#             import re
#             img_paths = re.findall(r'(?:[\w./\\-]+\.(?:png|jpg|jpeg|svg))', str(output))
#             for img_path in img_paths:
#                 if not os.path.isabs(img_path):
#                     abs_path = os.path.join(self.session_output_dir, img_path)
#                 else:
#                     abs_path = img_path
#                 if not os.path.exists(abs_path):
#                     missing_figures.append(img_path)
#             if missing_figures:
#                 feedback += f"\n⚠️ 检测到以下图片未生成成功: {missing_figures}\n建议重新分析本轮或修正代码后再试。"
#                 # 可以在这里返回一个特殊标志，供 analyze 主流程判断是否需要重启分析
#                 return {
#                     'action': 'generate_code',
#                     'code': code,
#                     'result': result,
#                     'feedback': feedback,
#                     'response': response,
#                     'continue': False,  # 终止本轮分析
#                     'need_restart': True,
#                     'missing_figures': missing_figures
#                 }
#             return {
#                 'action': 'generate_code',
#                 'code': code,
#                 'result': result,
#                 'feedback': feedback,
#                 'response': response,
#                 'continue': True
#             }
#         else:
#             print("⚠️ 未从响应中提取到可执行代码，要求LLM重新生成")
#             return {
#                 'action': 'invalid_response',
#                 'error': '响应中缺少可执行代码',
#                 'response': response,
#                 'continue': True
#             }
        
#     def analyze(self, user_input: str, files: List[str] = None) -> Dict[str, Any]:
#         """
#         开始分析流程
        
#         Args:
#             user_input: 用户的自然语言需求
#             files: 数据文件路径列表
            
#         Returns:
#             分析结果字典
#         """
#         # 重置状态
#         self.conversation_history = []
#         self.analysis_results = []
#         self.current_round = 0
        
#         # 创建本次分析的专用输出目录
#         self.session_output_dir = create_session_output_dir(self.base_output_dir,user_input)
        
#         # 初始化代码执行器，使用会话目录
#         self.executor = CodeExecutor(self.session_output_dir)
        
#         # 设置会话目录变量到执行环境中
#         self.executor.set_variable('session_output_dir', self.session_output_dir)
        
#         # 构建初始prompt
#         initial_prompt = f"""用户需求: {user_input}"""
#         if files:
#             initial_prompt += f"\n数据文件: {', '.join(files)}"
        
#         print(f"🚀 开始数据分析任务")
#         print(f"📝 用户需求: {user_input}")
#         if files:
#             print(f"📁 数据文件: {', '.join(files)}")
#         print(f"📂 输出目录: {self.session_output_dir}")
#         print(f"🔢 最大轮数: {self.max_rounds}")
#         print("=" * 60)
#           # 添加到对话历史
#         self.conversation_history.append({
#             'role': 'user',
#             'content': initial_prompt
#         })
        
#         while self.current_round < self.max_rounds:
#             self.current_round += 1
#             print(f"\n🔄 第 {self.current_round} 轮分析")
#               # 调用LLM生成响应
#             try:                
#                 # 获取当前执行环境的变量信息
#                 notebook_variables = self.executor.get_environment_info()
                
#                 # 格式化系统提示词，填入动态的notebook变量信息
#                 formatted_system_prompt = data_analysis_system_prompt.format(
#                     notebook_variables=notebook_variables
#                 )
                
#                 response = self.llm.call(
#                     prompt=self._build_conversation_prompt(),
#                     system_prompt=formatted_system_prompt
#                 )
                
#                 print(f"🤖 助手响应:\n{response}")
                
#                 # 使用统一的响应处理方法
#                 process_result = self._process_response(response)
                
#                 # 根据处理结果决定是否继续
#                 if not process_result.get('continue', True):
#                     print(f"\n✅ 分析完成！")
#                     break
                
#                 # 添加到对话历史
#                 self.conversation_history.append({
#                     'role': 'assistant',
#                     'content': response
#                 })
                
#                 # 根据动作类型添加不同的反馈
#                 if process_result['action'] == 'generate_code':
#                     feedback = process_result.get('feedback', '')
#                     self.conversation_history.append({
#                         'role': 'user',
#                         'content': f"代码执行反馈:\n{feedback}"
#                     })
                    
#                     # 记录分析结果
#                     self.analysis_results.append({
#                         'round': self.current_round,
#                         'code': process_result.get('code', ''),
#                         'result': process_result.get('result', {}),
#                         'response': response
#                     })                
#                 elif process_result['action'] == 'collect_figures':
#                     # 记录图片收集结果
#                     collected_figures = process_result.get('collected_figures', [])
#                     filtered_figures_to_collect = process_result.get('filtered_figures_to_collect', [])
#                     feedback = f"已收集 {len(collected_figures)} 个图片及其分析"
#                     self.conversation_history.append({
#                         'role': 'user', 
#                         'content': f"图片收集反馈:\n{feedback}\n请继续下一步分析。"
#                     })
#                     # 只记录过滤后的图片记忆
#                     self.analysis_results.append({
#                         'round': self.current_round,
#                         'action': 'collect_figures',
#                         'collected_figures': collected_figures,
#                         'filtered_figures_to_collect': filtered_figures_to_collect,
#                         'response': response
#                     })
           
#             except Exception as e:
#                 error_msg = f"LLM调用错误: {str(e)}"
#                 print(f"❌ {error_msg}")
#                 self.conversation_history.append({
#                     'role': 'user',
#                     'content': f"发生错误: {error_msg}，请重新生成代码。"
#                 })
#         # 生成最终总结
#         if self.current_round >= self.max_rounds:
#             print(f"\n⚠️ 已达到最大轮数 ({self.max_rounds})，分析结束")
        
#         return self._generate_final_report()
    
#     def _build_conversation_prompt(self) -> str:
#         """构建对话提示词"""
#         prompt_parts = []
        
#         for msg in self.conversation_history:
#             role = msg['role']
#             content = msg['content']
#             if role == 'user':
#                 prompt_parts.append(f"用户: {content}")
#             else:
#                 prompt_parts.append(f"助手: {content}")
        
#         return "\n\n".join(prompt_parts)
    
#     def _generate_final_report(self) -> Dict[str, Any]:
#         """生成最终分析报告"""
#         # 收集所有生成的图片信息
#         all_figures = []
#         for result in self.analysis_results:
#             if result.get('action') == 'collect_figures':
#                 all_figures.extend(result.get('collected_figures', []))
        
#         print(f"\n📊 开始生成最终分析报告...")
#         print(f"📂 输出目录: {self.session_output_dir}")
#         print(f"🔢 总轮数: {self.current_round}")
#         print(f"📈 收集图片: {len(all_figures)} 个")
        
#         # 构建用于生成最终报告的提示词
#         final_report_prompt = self._build_final_report_prompt(all_figures)
        
#         # 调用LLM生成最终报告
#         response = self.llm.call(
#             prompt=final_report_prompt,
#             system_prompt="你将会接收到一个数据分析任务的最终报告请求，请根据提供的分析结果和图片信息生成完整的分析报告。",
#             max_tokens=16384  
#         )
        
#         # 解析响应，提取最终报告
#         try:
#             yaml_data = self.llm.parse_yaml_response(response)
#             if yaml_data.get('action') == 'analysis_complete':
#                 final_report_content = yaml_data.get('final_report', '报告生成失败')
#             else:
#                 final_report_content = "LLM未返回analysis_complete动作，报告生成失败"
#         except:
#             # 如果解析失败，直接使用响应内容
#             final_report_content = response
        
#         print("✅ 最终报告生成完成")
#         # 手动添加附件清单到报告末尾
#         if all_figures:
#             appendix_section = "\n\n## 附件清单\n\n"
#             appendix_section += "本报告包含以下图片附件：\n\n"
            
#             for i, figure in enumerate(all_figures, 1):
#                 filename = figure.get('filename', '未知文件名')
#                 description = figure.get('description', '无描述')
#                 analysis = figure.get('analysis', '无分析')
#                 file_path = figure.get('file_path', '')
                
#                 appendix_section += f"{i}. **{filename}**\n"
#                 appendix_section += f"   - 描述：{description}\n"
#                 appendix_section += f"   - 细节分析：{analysis}\n"
#                 if self.absolute_path:
#                     appendix_section += f"   - 文件路径：{file_path}\n"
#                 else:
#                     appendix_section += f"   - 文件路径：./{filename}\n"
#                 appendix_section += "\n"
            
#             # 将附件清单添加到报告内容末尾
#             final_report_content += appendix_section
        
#         # 保存最终报告到文件
#         report_file_path = os.path.join(self.session_output_dir, "最终分析报告.md")
#         try:
#             with open(report_file_path, 'w', encoding='utf-8') as f:
#                 f.write(final_report_content)
#             print(f"📄 最终报告已保存至: {report_file_path}")
#             if all_figures:
#                 print(f"📎 已添加 {len(all_figures)} 个图片的附件清单")
#         except Exception as e:
#             print(f"❌ 保存报告文件失败: {str(e)}")
        
#         # 返回完整的分析结果
#         return {
#             'session_output_dir': self.session_output_dir,
#             'total_rounds': self.current_round,
#             'analysis_results': self.analysis_results,
#             'collected_figures': all_figures,
#             'conversation_history': self.conversation_history,
#             'final_report': final_report_content,
#             'report_file_path': report_file_path       
#             }

#     def _build_final_report_prompt(self, all_figures: List[Dict[str, Any]]) -> str:
#         """构建用于生成最终报告的提示词"""
        
#         # 构建图片信息摘要，使用相对路径
#         figures_summary = ""
#         if all_figures:
#             figures_summary = "\n生成的图片及分析:\n"
#             for i, figure in enumerate(all_figures, 1):
#                 filename = figure.get('filename', '未知文件名')
#                 # 使用相对路径格式，适合在报告中引用
#                 relative_path = f"./{filename}"
#                 figures_summary += f"{i}. {filename}\n"
#                 figures_summary += f"   路径: {relative_path}\n"
#                 figures_summary += f"   描述: {figure.get('description', '无描述')}\n"
#                 figures_summary += f"   分析: {figure.get('analysis', '无分析')}\n\n"
#         else:
#             figures_summary = "\n本次分析未生成图片。\n"
        
#         # 构建代码执行结果摘要（仅包含成功执行的代码块）
#         code_results_summary = ""
#         success_code_count = 0
#         for result in self.analysis_results:
#             if result.get('action') != 'collect_figures' and result.get('code'):
#                 exec_result = result.get('result', {})
#                 if exec_result.get('success'):
#                     success_code_count += 1
#                     code_results_summary += f"代码块 {success_code_count}: 执行成功\n"
#                     if exec_result.get('output'):
#                         code_results_summary += f"输出: {exec_result.get('output')[:]}\n\n"

        
#         # 使用 prompts.py 中的统一提示词模板，并添加相对路径使用说明
#         pre_prompt = final_report_system_prompt_absolute if self.absolute_path else final_report_system_prompt
#         prompt = pre_prompt.format(
#             current_round=self.current_round,
#             session_output_dir=self.session_output_dir,
#             figures_summary=figures_summary,
#             code_results_summary=code_results_summary
#         )
        
#         return prompt

#     def reset(self):
#         """重置智能体状态"""
#         self.conversation_history = []
#         self.analysis_results = []
#         self.current_round = 0
#         if self.executor:
#             self.executor.reset_environment()

#     async def async_analyze(self, user_input: str, files: List[str] = None) -> Dict[str, Any]:
#         """
#         异步分析方法 - 利用 LLMHelper 的异步能力
#         参照 data_analysis.py 的结构实现

#         Args:
#             user_input: 用户的自然语言需求
#             files: 数据文件路径列表

#         Returns:
#             分析结果字典
#         """
#         # 重置状态
#         self.conversation_history = []
#         self.analysis_results = []
#         self.current_round = 0

#         # 创建本次分析的专用输出目录
#         self.session_output_dir = create_session_output_dir(self.base_output_dir, user_input)

#         # 初始化代码执行器，使用会话目录
#         self.executor = CodeExecutor(self.session_output_dir)

#         # 设置会话目录变量到执行环境中
#         self.executor.set_variable('session_output_dir', self.session_output_dir)

#         # 构建初始prompt
#         initial_prompt = f"""用户需求: {user_input}"""
#         if files:
#             initial_prompt += f"\n数据文件: {', '.join(files)}"

#         print(f"🚀 开始异步数据分析任务")
#         print(f"📝 用户需求: {user_input}")
#         if files:
#             print(f"📁 数据文件: {', '.join(files)}")
#         print(f"📂 输出目录: {self.session_output_dir}")
#         print(f"🔢 最大轮数: {self.max_rounds}")
#         print(f"🔧 LLM配置: {self.config}")
#         print("=" * 60)

#         # 添加到对话历史
#         self.conversation_history.append({
#             'role': 'user',
#             'content': initial_prompt
#         })

#         while self.current_round < self.max_rounds:
#             self.current_round += 1
#             print(f"\n🔄 第 {self.current_round} 轮异步分析")

#             try:
#                 # 获取当前执行环境的变量信息
#                 notebook_variables = self.executor.get_environment_info()

#                 # 格式化系统提示词，填入动态的notebook变量信息
#                 formatted_system_prompt = data_analysis_system_prompt.format(
#                     notebook_variables=notebook_variables
#                 )

#                 # 使用异步 LLM 助手调用
#                 response = await self.llm.async_call(
#                     prompt=self._build_conversation_prompt(),
#                     system_prompt=formatted_system_prompt
#                 )

#                 print(f"🤖 助手响应:\n{response}")

#                 # 使用统一的响应处理方法
#                 process_result = self._process_response(response)

#                 # 根据处理结果决定是否继续
#                 if not process_result.get('continue', True):
#                     print(f"\n✅ 异步分析完成！")
#                     break

#                 # 添加到对话历史
#                 self.conversation_history.append({
#                     'role': 'assistant',
#                     'content': response
#                 })

#                 # 根据动作类型添加不同的反馈
#                 if process_result['action'] == 'generate_code':
#                     feedback = process_result.get('feedback', '')
#                     self.conversation_history.append({
#                         'role': 'user',
#                         'content': f"代码执行反馈:\n{feedback}"
#                     })

#                     # 记录分析结果
#                     self.analysis_results.append({
#                         'round': self.current_round,
#                         'code': process_result.get('code', ''),
#                         'result': process_result.get('result', {}),
#                         'response': response
#                     })

#                 elif process_result['action'] == 'collect_figures':
#                     # 记录图片收集结果
#                     collected_figures = process_result.get('collected_figures', [])
#                     feedback = f"已收集 {len(collected_figures)} 个图片及其分析"
#                     self.conversation_history.append({
#                         'role': 'user',
#                         'content': f"图片收集反馈:\n{feedback}\n请继续下一步分析。"
#                     })

#                     self.analysis_results.append({
#                         'round': self.current_round,
#                         'action': 'collect_figures',
#                         'collected_figures': collected_figures,
#                         'response': response
#                     })

#             except Exception as e:
#                 error_msg = f"异步LLM调用错误: {str(e)}"
#                 print(f"❌ {error_msg}")
#                 self.conversation_history.append({
#                     'role': 'user',
#                     'content': f"发生错误: {error_msg}，请重新生成代码。"
#                 })

#         # 生成最终总结
#         if self.current_round >= self.max_rounds:
#             print(f"\n⚠️ 已达到最大轮数 ({self.max_rounds})，异步分析结束")

#         return self._generate_final_report()

#     async def close(self):
#         """关闭LLM客户端连接"""
#         if self.llm:
#             await self.llm.close()
#             print("🔌 LLM客户端连接已关闭")

#     def get_config_info(self) -> Dict[str, Any]:
#         """获取当前配置信息"""
#         return {
#             'llm_config': self.config.to_dict(),
#             'output_dir': self.base_output_dir,
#             'max_rounds': self.max_rounds,
#             'absolute_path': self.absolute_path,
#             'current_round': self.current_round,
#             'session_output_dir': self.session_output_dir
#         }

#     def update_config(self, new_config: LLMConfig):
#         """更新LLM配置"""
#         self.config = new_config
#         if not self.config.validate():
#             print("⚠️ 新的LLM配置验证失败，请检查配置")
#         self.llm = LLMHelper(self.config)
#         print(f"✅ LLM配置已更新: {self.config}")

#     def get_analysis_summary(self) -> Dict[str, Any]:
#         """获取分析摘要"""
#         return {
#             'total_rounds': self.current_round,
#             'analysis_results_count': len(self.analysis_results),
#             'conversation_length': len(self.conversation_history),
#             'session_output_dir': self.session_output_dir,
#             'config_used': self.config.to_dict()
#         }
