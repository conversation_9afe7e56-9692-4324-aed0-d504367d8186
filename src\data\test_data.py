import pandas as pd
import os

try:
    # 尝试读取数据
    print("正在尝试读取数据...")
    df = pd.read_json("hf://datasets/Josephgflowers/Finance-Instruct-500k/train.json", lines=True)
    
    print(f"成功读取了 {len(df)} 条记录")
    print(f"数据列名: {list(df.columns)}")
    
    # 显示前三条记录
    print("\n前三条记录:")
    print(df.head(3))
    
    # 测试保存到临时文件
    temp_path = "temp_test.csv"
    print(f"\n正在保存到临时文件: {temp_path}")
    df.to_csv(temp_path, index=False, encoding='utf-8-sig')
    
    # 读取回来验证编码
    print("正在验证保存的数据...")
    df_test = pd.read_csv(temp_path, encoding='utf-8-sig')
    print(f"验证成功，读取回 {len(df_test)} 条记录")
    
    # 清理临时文件
    if os.path.exists(temp_path):
        os.remove(temp_path)
        print("临时文件已清理")
    
except Exception as e:
    print(f"发生错误: {e}")
    print("请确保已登录Hugging Face: huggingface-cli login") 