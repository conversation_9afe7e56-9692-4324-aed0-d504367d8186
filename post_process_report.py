#!/usr/bin/env python3
"""
深度报告后处理模块
对生成的深度报告进行格式整理和内容优化
确保图片路径不变且能正确显示
"""

import sys
import os
import glob
import re
import time
sys.path.append('./src')

from report_generator import IntegratedResearchReportGenerator

def post_process_report(report_file_path):
    """
    对深度报告进行后处理
    
    Args:
        report_file_path: 深度报告文件路径
    
    Returns:
        str: 处理后的报告文件路径
    """
    print("🔧 开始对深度报告进行后处理...")
    print(f"📄 原始报告: {report_file_path}")
    
    if not os.path.exists(report_file_path):
        print(f"❌ 报告文件不存在: {report_file_path}")
        return None
    
    # 读取原始报告内容
    with open(report_file_path, 'r', encoding='utf-8') as f:
        original_content = f.read()
    
    print(f"📊 原始报告统计:")
    print(f"  字符数: {len(original_content):,}")
    print(f"  行数: {len(original_content.split(chr(10)))}")
    
    # 提取所有图片引用
    image_refs = re.findall(r'!\[([^\]]*)\]\(([^)]+)\)', original_content)
    print(f"  图片数量: {len(image_refs)}")
    
    # 验证图片存在性
    valid_images = []
    invalid_images = []
    
    for alt_text, img_path in image_refs:
        from urllib.parse import unquote
        decoded_path = unquote(img_path)
        
        if os.path.exists(img_path) or os.path.exists(decoded_path):
            valid_images.append((alt_text, img_path))
        else:
            invalid_images.append((alt_text, img_path))
    
    print(f"  有效图片: {len(valid_images)}")
    print(f"  无效图片: {len(invalid_images)}")
    
    # 创建生成器实例用于LLM调用
    generator = IntegratedResearchReportGenerator(
        target_company="商汤科技",
        target_company_code="00020", 
        target_company_market="HK",
        search_engine="tavily"
    )
    
    # 构建后处理prompt
    post_process_prompt = f"""
你是一位专业的财务报告编辑专家。请对以下深度财务研报进行格式整理和内容优化，要求：

**格式要求：**
1. 确保所有标题（## 和 ###）独占一行，前后有适当空行
2. 段落之间有清晰的空行分隔
3. 列表项格式规范，使用统一的编号或符号
4. 表格格式整齐，对齐美观
5. 图片前后有适当的空行，确保版面美观

**内容保持严格要求（绝对不能删减）：**
1. 必须保持所有原始数据和分析内容100%完整，绝对不能简写或删减
2. 必须保持所有数字、百分比、金额等数据完全一致
3. 必须保持所有表格、列表的完整性，不能合并或省略
4. 必须保持原有的分析深度和详细程度
5. 只做格式美化，不做内容精简
6. 确保输出报告的字符数不少于原报告的95%

**图片处理严格要求：**
1. 必须保持所有图片引用完全不变
2. 图片路径一个字符都不能修改
3. 图片的alt文本可以适当优化，但路径绝对不能改变
4. 确保图片在合适的位置，与相关内容紧密结合

**当前报告中的有效图片列表（必须全部保留）：**
{chr(10).join([f'- ![{alt}]({path})' for alt, path in valid_images])}

**重要提醒：**
- 这是一份{len(original_content)}字符的详细深度报告
- 请确保输出的报告字符数不少于{int(len(original_content) * 0.95)}字符
- 只做格式美化，绝对不做内容删减
- 保持原有的专业分析深度和详细程度

**原始报告内容：**

{original_content}

请返回格式整理后的完整报告，确保内容详细程度与原报告完全一致，绝对不能简化或删减任何内容。
"""
    
    print(f"\n🤖 调用LLM进行后处理...")
    
    try:
        # 调用LLM进行后处理
        processed_content = generator.llm.call(post_process_prompt)
        
        print(f"✅ LLM后处理完成")
        
        # 验证处理后的图片引用
        processed_image_refs = re.findall(r'!\[([^\]]*)\]\(([^)]+)\)', processed_content)
        
        print(f"\n📊 处理后报告统计:")
        print(f"  字符数: {len(processed_content):,}")
        print(f"  行数: {len(processed_content.split(chr(10)))}")
        print(f"  图片数量: {len(processed_image_refs)}")
        
        # 检查图片是否丢失
        original_paths = set([path for _, path in image_refs])
        processed_paths = set([path for _, path in processed_image_refs])
        
        missing_images = original_paths - processed_paths
        added_images = processed_paths - original_paths
        
        if missing_images:
            print(f"⚠️ 发现丢失的图片: {len(missing_images)}")
            for img in missing_images:
                print(f"  - {os.path.basename(img)}")
        
        if added_images:
            print(f"⚠️ 发现新增的图片: {len(added_images)}")
            for img in added_images:
                print(f"  - {os.path.basename(img)}")
        
        # 如果图片有变化，进行修复
        if missing_images or added_images:
            print(f"🔧 修复图片引用...")
            processed_content = fix_image_references(processed_content, valid_images)
            
            # 重新验证
            final_image_refs = re.findall(r'!\[([^\]]*)\]\(([^)]+)\)', processed_content)
            print(f"  修复后图片数量: {len(final_image_refs)}")
        
        # 生成处理后的文件名
        base_name = os.path.splitext(report_file_path)[0]
        processed_file_path = f"{base_name}_优化版.md"
        
        # 保存处理后的报告
        with open(processed_file_path, 'w', encoding='utf-8') as f:
            f.write(processed_content)
        
        print(f"\n✅ 后处理完成！")
        print(f"📁 优化后报告: {processed_file_path}")
        
        return processed_file_path
        
    except Exception as e:
        print(f"❌ 后处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def fix_image_references(content, valid_images):
    """修复图片引用，确保所有有效图片都被包含"""
    print(f"    🔧 修复图片引用...")
    
    # 移除所有现有的图片引用
    content_without_images = re.sub(r'!\[([^\]]*)\]\(([^)]+)\)', '', content)
    
    # 按章节重新插入图片
    sections = content_without_images.split('## ')
    
    # 为每个章节分配图片
    images_per_section = len(valid_images) // max(len(sections) - 1, 1)
    remaining_images = len(valid_images) % max(len(sections) - 1, 1)
    
    image_index = 0
    processed_sections = [sections[0]]  # 保留标题部分
    
    for i, section in enumerate(sections[1:], 1):
        section_content = f"## {section}"
        
        # 计算这个章节应该有多少图片
        section_image_count = images_per_section
        if i <= remaining_images:
            section_image_count += 1
        
        # 在章节中间插入图片
        if section_image_count > 0 and image_index < len(valid_images):
            paragraphs = section_content.split('\n\n')
            
            # 在章节中间位置插入图片
            insert_positions = []
            if len(paragraphs) > 2:
                step = len(paragraphs) // (section_image_count + 1)
                for j in range(section_image_count):
                    pos = (j + 1) * step
                    if pos < len(paragraphs):
                        insert_positions.append(pos)
            
            # 如果没有合适的位置，就在章节末尾插入
            if not insert_positions:
                insert_positions = [len(paragraphs) - 1]
            
            # 插入图片
            for pos in reversed(insert_positions):
                if image_index < len(valid_images):
                    alt_text, img_path = valid_images[image_index]
                    image_markdown = f"\n\n![{alt_text}]({img_path})\n\n"
                    paragraphs.insert(pos, image_markdown.strip())
                    image_index += 1
            
            section_content = '\n\n'.join(paragraphs)
        
        processed_sections.append(section_content)
    
    # 插入剩余的图片到最后一个章节
    while image_index < len(valid_images):
        alt_text, img_path = valid_images[image_index]
        image_markdown = f"\n\n![{alt_text}]({img_path})\n\n"
        processed_sections[-1] += image_markdown
        image_index += 1
    
    return ''.join(processed_sections)

def find_latest_report():
    """查找最新的深度报告文件"""
    pattern = "深度财务研报分析_*.md"
    files = glob.glob(pattern)
    
    if not files:
        print("❌ 未找到深度报告文件")
        return None
    
    # 排除已经优化过的文件
    files = [f for f in files if not f.endswith('_优化版.md')]
    
    if not files:
        print("❌ 未找到未优化的深度报告文件")
        return None
    
    # 返回最新的文件
    latest_file = max(files, key=os.path.getctime)
    return latest_file

def main():
    """主函数"""
    print("🚀 深度报告后处理工具")
    print("=" * 60)
    
    # 查找最新的深度报告
    latest_report = find_latest_report()
    
    if not latest_report:
        print("💡 请先生成深度报告")
        return
    
    print(f"📄 找到最新报告: {latest_report}")
    
    # 进行后处理
    processed_report = post_process_report(latest_report)
    
    if processed_report:
        print(f"\n🎉 后处理成功完成！")
        print(f"📁 原始报告: {latest_report}")
        print(f"📁 优化报告: {processed_report}")
        print(f"\n✨ 优化内容包括：")
        print(f"  ✅ 格式整理（标题、段落、列表）")
        print(f"  ✅ 内容优化（语言表达、逻辑结构）")
        print(f"  ✅ 图片保护（路径完全不变）")
        print(f"  ✅ 版面美化（空行、对齐）")
    else:
        print(f"\n❌ 后处理失败！")

if __name__ == "__main__":
    main()
