#!/usr/bin/env python3
"""
紧急格式修复
专门处理极端复杂的格式问题
"""

import re
import os

def emergency_format_fix(file_path):
    """紧急格式修复，处理极端复杂的格式问题"""
    print(f"🚨 紧急格式修复: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    # 读取文件
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📊 原始统计:")
    print(f"  字符数: {len(content):,}")
    print(f"  行数: {len(content.split(chr(10)))}")
    
    # 应用超强格式修复
    fixed_content = super_emergency_fix(content)
    
    print(f"📊 修复后统计:")
    print(f"  字符数: {len(fixed_content):,}")
    print(f"  行数: {len(fixed_content.split(chr(10)))}")
    
    # 保存修复后的文件
    fixed_file = file_path.replace('.md', '_紧急修复版.md')
    with open(fixed_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"✅ 紧急修复完成: {fixed_file}")
    
    # 验证修复效果
    title_lines = [line for line in fixed_content.split('\n') if line.startswith('##')]
    mixed_titles = [line for line in title_lines if len(line.split()) > 15]
    
    print(f"📝 修复效果:")
    print(f"  标题行数: {len(title_lines)}")
    print(f"  混合标题: {len(mixed_titles)} 个")
    
    if len(mixed_titles) == 0:
        print(f"  ✅ 所有标题格式完美")
        return True
    else:
        print(f"  ⚠️ 仍有 {len(mixed_titles)} 个标题格式问题")
        return False

def super_emergency_fix(content):
    """超强紧急格式修复算法"""
    if not content or len(content.strip()) < 100:
        return content
    
    print(f"🔧 开始超强紧急格式修复...")
    
    # 1. 预处理：解码URL编码的图片路径
    content = re.sub(r'%E[0-9A-F]%[0-9A-F][0-9A-F]%[0-9A-F][0-9A-F]', lambda m: bytes.fromhex(m.group().replace('%', '')).decode('utf-8', errors='ignore'), content)
    
    # 2. 强制分离所有标题
    # 处理 "## 标题 ### 子标题 内容" 的复杂模式
    content = re.sub(r'(##\s+[^#\n]+?)\s+(###\s+[^#\n]+?)\s+([^#])', r'\1\n\n\2\n\n\3', content)
    
    # 处理 "## 标题 内容" 的模式
    content = re.sub(r'(##\s+[^#\n]+?)\s+([^#][^。！？]*?[。！？])', r'\1\n\n\2', content)
    
    # 处理 "### 标题 内容" 的模式
    content = re.sub(r'(###\s+[^#\n]+?)\s+([^#][^。！？]*?[。！？])', r'\1\n\n\2', content)
    
    # 3. 处理图片格式
    # 先分离图片和周围内容
    content = re.sub(r'([^。！？\n])\s*(!\[[^\]]*\]\([^)]+\))\s*([^。！？\n])', r'\1\n\n\2\n\n\3', content)
    
    # 4. 逐字符强制处理
    chars = list(content)
    result_chars = []
    i = 0
    
    while i < len(chars):
        char = chars[i]
        
        # 检查是否是标题开始
        if char == '#' and i + 1 < len(chars) and chars[i + 1] == '#':
            # 找到这个标题的结束位置
            title_start = i
            title_end = i + 2  # 跳过 ##
            
            # 跳过空格
            while title_end < len(chars) and chars[title_end] == ' ':
                title_end += 1
            
            # 找到标题文本的结束
            while title_end < len(chars) and chars[title_end] not in '\n':
                if chars[title_end] in ' ' and title_end + 1 < len(chars):
                    # 检查后面是否是内容开始
                    next_char = chars[title_end + 1]
                    if next_char not in '#' and not next_char.isspace() and next_char.isalpha():
                        # 这里是标题和内容的分界点
                        break
                title_end += 1
            
            # 添加标题
            title_text = ''.join(chars[title_start:title_end]).strip()
            result_chars.extend(list(title_text))
            result_chars.extend(['\n', '\n'])
            
            i = title_end
            continue
        
        result_chars.append(char)
        i += 1
    
    content = ''.join(result_chars)
    
    # 5. 强制在每个句号后换行
    sentences = re.split(r'([。！？])', content)
    formatted_sentences = []
    
    for i in range(0, len(sentences), 2):
        if i + 1 < len(sentences):
            sentence = sentences[i] + sentences[i + 1]
            sentence = sentence.strip()
            if sentence:
                # 检查是否是标题行
                if sentence.startswith('#'):
                    formatted_sentences.append(sentence + '\n\n')
                else:
                    formatted_sentences.append(sentence + '\n\n')
        else:
            if sentences[i].strip():
                formatted_sentences.append(sentences[i].strip())
    
    content = ''.join(formatted_sentences)
    
    # 6. 清理和优化
    # 清理多余空行
    content = re.sub(r'\n{4,}', '\n\n\n', content)
    
    # 确保标题前有空行
    content = re.sub(r'([^\n])\n(##)', r'\1\n\n\n\2', content)
    content = re.sub(r'([^\n])\n(###)', r'\1\n\n\2', content)
    
    # 处理图片独占行
    content = re.sub(r'([^\n])(!\[[^\]]*\]\([^)]+\))([^\n])', r'\1\n\n\2\n\n\3', content)
    
    # 处理表格格式
    content = re.sub(r'([^\n])(\|[^|\n]+\|)', r'\1\n\n\2', content)
    
    # 最终清理
    content = content.strip()
    
    return content

def main():
    """主函数"""
    print("🚨 紧急格式修复工具")
    print("=" * 60)
    
    # 查找最新的深度报告
    import glob
    
    pattern = "深度财务研报分析_*.md"
    files = glob.glob(pattern)
    
    if not files:
        print("❌ 未找到深度报告文件")
        return
    
    # 排除已经修复过的文件
    files = [f for f in files if not f.endswith('_紧急修复版.md')]
    
    if not files:
        print("❌ 未找到原始深度报告文件")
        return
    
    latest_file = max(files, key=os.path.getctime)
    print(f"📄 处理文件: {latest_file}")
    
    # 应用紧急修复
    success = emergency_format_fix(latest_file)
    
    if success:
        print(f"\n🎉 紧急修复成功！")
        print(f"📁 修复后文件: {latest_file.replace('.md', '_紧急修复版.md')}")
    else:
        print(f"\n⚠️ 修复效果有限，需要进一步优化")

if __name__ == "__main__":
    main()
