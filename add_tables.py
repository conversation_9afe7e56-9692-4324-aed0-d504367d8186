#!/usr/bin/env python3
"""
向深度财务研报中添加重要表格
"""

import sys

def add_tables_to_report(md_file_path):
    """向深度报告中添加重要表格"""
    
    # 读取深度报告
    with open(md_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义要添加的表格
    risk_opportunity_table = """
### 风险与机遇矩阵

| 维度         | 机遇                                                                 | 风险                                                                 |
|--------------|----------------------------------------------------------------------|----------------------------------------------------------------------|
| **技术**     | 多模态模型在影视创作（如Seko AI）的千亿级市场渗透                   | 量子计算可能颠覆现有AI算法架构                                       |
| **政策**     | 新质生产力政策下，地方政府AI采购预算年增20%                         | 中美技术脱钩导致GPU供应不稳定                                        |
| **市场**     | 东南亚智慧城市项目（如新加坡"智慧岛2.0"）带来50亿元增量空间         | 企业IT预算收缩导致智慧商业项目延期                                   |

"""
    
    business_model_table = """
### 商业模式与战略差异对比

| **维度**         | **商汤科技**                | **旷视科技**              | **百度**                | **华为**              |
|-------------------|-----------------------------|---------------------------|-------------------------|-----------------------|
| **核心模式**      | 1+X（通用大模型+行业定制） | 城市IoT+供应链硬件        | 搜索+文心大模型（C端） | 昇腾芯片+政务云      |
| **收入结构**      | 生成式AI占64%               | 硬件销售占比超50%         | 广告业务主导            | 算力租赁+芯片销售    |
| **行业覆盖**      | 金融、汽车、医疗、政务等10+领域 | 智慧城市、物流仓储        | 教育、营销              | 政务、通信运营商      |
| **生态合作**      | 小米、广汽等30+车企         | 阿里云、顺丰              | 手机厂商预装            | 地方政府、央企        |
| **国际化**        | 香港V2X项目、东南亚智慧城市 | 无显著进展                | 北美受限                | 中东、拉美市场拓展    |

"""
    
    # 在适当位置插入表格
    # 在风险分析章节添加风险与机遇矩阵
    if "## 风险提示与展望" in content:
        content = content.replace(
            "## 风险提示与展望",
            f"## 风险提示与展望\n\n{risk_opportunity_table}"
        )

    # 在行业分析章节添加商业模式对比表
    if "## 行业分析与竞争环境" in content:
        content = content.replace(
            "## 行业分析与竞争环境",
            f"## 行业分析与竞争环境\n\n{business_model_table}"
        )
    
    # 保存修改后的文件
    output_file = md_file_path.replace('.md', '_完整版.md')
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"已添加表格到报告中")
    print(f"输出文件: {output_file}")
    
    return output_file

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python add_tables.py <深度报告文件路径>")
        sys.exit(1)
    
    md_file = sys.argv[1]
    output_file = add_tables_to_report(md_file)
    print(f"\n✅ 表格添加完成: {output_file}")
