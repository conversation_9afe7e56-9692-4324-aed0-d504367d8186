#!/usr/bin/env python3
"""
测试图片提取功能
"""

import os
import re

def test_image_extraction():
    """测试从财务研报中提取图片"""
    
    # 读取财务研报汇总内容
    with open('财务研报汇总_20250731_215448.md', 'r', encoding='utf-8') as f:
        report_content = f.read()
    
    # 从报告内容中提取所有图片引用
    image_pattern = re.compile(r'!\[([^\]]*)\]\(([^)]+)\)')
    matches = image_pattern.findall(report_content)
    
    print(f"在财务研报汇总中发现 {len(matches)} 个图片引用:")
    
    available_images = []
    for i, (alt_text, img_path) in enumerate(matches, 1):
        print(f"{i}. 图片描述: {alt_text}")
        print(f"   图片路径: {img_path}")
        
        # 检查图片文件是否实际存在
        # 尝试多个可能的路径
        possible_paths = [
            img_path,  # 原始路径
            f"./images/{os.path.basename(img_path)}",  # images目录
            f"images/{os.path.basename(img_path)}"  # 相对images目录
        ]

        found_path = None
        for path in possible_paths:
            if os.path.exists(path):
                found_path = path
                break

        if found_path:
            available_images.append({
                'alt_text': alt_text,
                'path': found_path,
                'original_path': img_path,
                'filename': os.path.basename(img_path)
            })
            print(f"   状态: ✅ 存在 (实际路径: {found_path})")
        else:
            print(f"   状态: ❌ 不存在")
        print()
    
    print(f"实际存在的图片: {len(available_images)}")
    
    # 按章节分类
    sections = {
        "资产负债": ["资产", "负债", "偿债", "结构"],
        "盈利能力": ["收入", "成本", "利润", "费用", "研发"],
        "现金流": ["现金流", "现金"],
        "其他": []
    }
    
    for section, keywords in sections.items():
        section_images = []
        for img in available_images:
            if any(keyword in img['alt_text'] or keyword in img['filename'] for keyword in keywords):
                section_images.append(img)
        
        print(f"\n{section}相关图片 ({len(section_images)}个):")
        for img in section_images:
            print(f"  - {img['alt_text']}: {img['path']}")

if __name__ == "__main__":
    test_image_extraction()
