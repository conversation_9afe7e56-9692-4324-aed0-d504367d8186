#!/usr/bin/env python3
"""
分析extract_relevant_content导致的格式问题
找出问题根源并提供解决方案
"""

import sys
import os
import glob
import re
sys.path.append('./src')

from report_generator import IntegratedResearchReportGenerator

def analyze_extract_content_issue():
    """分析extract_relevant_content的问题"""
    print("🔍 分析extract_relevant_content导致的格式问题...")
    
    # 查找现有的基础分析报告
    md_files = glob.glob("财务研报汇总_*_images.md")
    if not md_files:
        print("❌ 未找到images版本的基础分析报告文件")
        return
    
    # 使用最新的images文件
    images_md = max(md_files, key=os.path.getctime)
    print(f"📊 分析文件: {images_md}")
    
    # 读取报告内容
    with open(images_md, 'r', encoding='utf-8') as f:
        report_content = f.read()
    
    # 创建生成器实例
    generator = IntegratedResearchReportGenerator(
        target_company="商汤科技",
        target_company_code="00020", 
        target_company_market="HK",
        search_engine="tavily"
    )
    
    # 获取内容映射
    content_mapping = generator.get_section_content_mapping(report_content)
    
    # 测试几个章节的内容提取
    test_sections = [
        "资产负债结构分析",
        "盈利能力与成本分析", 
        "现金流量分析"
    ]
    
    for section_name in test_sections:
        print(f"\n" + "="*80)
        print(f"🧪 测试章节: {section_name}")
        print(f"="*80)
        
        section_info = content_mapping.get(section_name, {})
        source_sections = section_info.get("source_sections", [])
        
        print(f"📚 源章节: {source_sections}")
        
        if source_sections:
            # 提取相关内容
            relevant_content = generator.extract_relevant_content(report_content, source_sections)
            
            print(f"\n📊 提取内容统计:")
            print(f"  内容长度: {len(relevant_content)} 字符")
            print(f"  内容行数: {len(relevant_content.split(chr(10)))}")
            
            # 分析格式问题
            format_issues = analyze_content_format(relevant_content)
            
            print(f"\n📝 格式问题分析:")
            for issue_type, count in format_issues.items():
                if count > 0:
                    print(f"  ❌ {issue_type}: {count} 个")
                else:
                    print(f"  ✅ {issue_type}: 无问题")
            
            # 显示内容预览
            print(f"\n📖 提取内容预览（前300字符）:")
            print("-" * 60)
            preview = relevant_content[:300] + "..." if len(relevant_content) > 300 else relevant_content
            print(preview)
            print("-" * 60)
            
            # 保存提取内容用于分析
            with open(f'extracted_{section_name}.md', 'w', encoding='utf-8') as f:
                f.write(relevant_content)
            print(f"📁 提取内容已保存到: extracted_{section_name}.md")

def analyze_content_format(content):
    """分析内容的格式问题"""
    issues = {
        "长行问题": 0,
        "标题格式问题": 0,
        "图片格式问题": 0,
        "表格格式问题": 0,
        "段落分隔问题": 0
    }
    
    lines = content.split('\n')
    
    # 检查长行问题
    for line in lines:
        if len(line) > 200:
            issues["长行问题"] += 1
    
    # 检查标题格式问题
    for line in lines:
        if line.startswith('#') and len(line) > 50:
            issues["标题格式问题"] += 1
    
    # 检查图片格式问题
    image_lines = [line for line in lines if '![' in line and '](' in line]
    for line in image_lines:
        if len(line) > 100:  # 图片行太长，可能和其他内容混在一起
            issues["图片格式问题"] += 1
    
    # 检查表格格式问题
    table_lines = [line for line in lines if '|' in line]
    for i, line in enumerate(table_lines):
        if i > 0 and not table_lines[i-1].strip():  # 表格前没有空行
            issues["表格格式问题"] += 1
    
    # 检查段落分隔问题
    paragraphs = content.split('\n\n')
    if len(paragraphs) < len(lines) / 10:  # 段落太少，说明分隔有问题
        issues["段落分隔问题"] = 1
    
    return issues

def create_clean_extract_solution():
    """创建干净的内容提取解决方案"""
    print(f"\n💡 创建干净的内容提取解决方案...")
    
    solution_code = '''
def extract_clean_relevant_content(self, report_content, source_sections):
    """提取干净的相关内容，避免格式污染"""
    if not source_sections:
        return ""

    clean_parts = []
    lines = report_content.split('\\n')

    for section_title in source_sections:
        # 查找章节开始位置
        section_start = -1
        for i, line in enumerate(lines):
            if line.strip() == section_title:
                section_start = i
                break

        if section_start == -1:
            continue

        # 查找章节结束位置
        section_end = len(lines)
        for i in range(section_start + 1, len(lines)):
            if lines[i].startswith('# ') and not lines[i].startswith('## '):
                section_end = i
                break

        # 提取并清理章节内容
        section_lines = lines[section_start:section_end]
        clean_content = self.clean_extracted_content(section_lines)
        
        if clean_content:
            clean_parts.append(clean_content)

    return '\\n\\n'.join(clean_parts)

def clean_extracted_content(self, section_lines):
    """清理提取的内容，去除格式问题"""
    import re
    
    clean_lines = []
    
    for line in section_lines:
        line = line.strip()
        
        # 跳过标题行
        if line.startswith('#'):
            continue
            
        # 跳过空行
        if not line:
            continue
            
        # 跳过表格分隔符
        if re.match(r'^[|\\s-]+$', line):
            continue
            
        # 处理过长的行，按句号分割
        if len(line) > 200:
            sentences = re.split(r'([。！？])', line)
            for i in range(0, len(sentences), 2):
                if i + 1 < len(sentences):
                    sentence = sentences[i] + sentences[i + 1]
                    sentence = sentence.strip()
                    if sentence and len(sentence) > 20:
                        clean_lines.append(sentence)
        else:
            # 只保留包含关键信息的行
            if any(keyword in line for keyword in ['亿元', '%', '比率', '增长', '下降', '提升', '占比', '万元']):
                clean_lines.append(line)
    
    # 将清理后的内容组合成段落
    if clean_lines:
        # 每3-4句组成一个段落
        paragraphs = []
        current_paragraph = []
        
        for line in clean_lines:
            current_paragraph.append(line)
            if len(current_paragraph) >= 3:
                paragraphs.append(' '.join(current_paragraph))
                current_paragraph = []
        
        # 处理剩余的句子
        if current_paragraph:
            paragraphs.append(' '.join(current_paragraph))
        
        return '\\n\\n'.join(paragraphs)
    
    return ""
'''
    
    print("✅ 解决方案代码已生成")
    return solution_code

def test_clean_extract():
    """测试干净的内容提取"""
    print(f"\n🧪 测试干净的内容提取方案...")
    
    # 模拟有问题的原始内容
    problematic_content = """# 商汤科技资产负债表分析

本次分析聚焦于商汤科技2018-2024年的财务结构演变，通过资产负债表数据揭示其资产配置战略转型和偿债能力变化。 分析采用趋势分析、结构分析和比率分析等方法，旨在评估公司从轻资产模式向重资产转型过程中的财务特征与潜在风险。 分析首先对原始数据进行清洗验证，确保"流动资产合计"、"非流动资产合计"等关键字段的完整性。 随后通过三组可视化分析：资产规模与结构趋势、偿债能力核心指标演变、以及主要资产构成变化，系统性地解构公司财务特征。 所有计算均基于经审计的年度报告数据。 商汤科技展现出明显的战略转型特征，总资产规模实现12.7%的年复合增长，但资产结构发生根本性重构。 2021年成为关键转折点，此后非流动资产加速增长79%，导致流动资产占比从81%降至53%。 尽管资产负债率从154%的历史高点改善至31.67%的健康水平，但流动比率持续下降62%值得警惕。 物业厂房设备投资占比提升至21.3%，反映公司正构建实体基础设施壁垒。

![资产结构演变](./images/资产结构趋势分析.png)

图表清晰呈现公司资产配置的战略转向。 2018-2020年期间，流动资产占比维持在80%以上的典型轻资产特征，主要由货币资金（42%）和应收账款（31%）构成。"""
    
    print(f"原始内容（有格式问题）:")
    print("-" * 60)
    print(problematic_content[:300] + "...")
    print("-" * 60)
    print(f"原始行数: {len(problematic_content.split(chr(10)))}")
    
    # 模拟清理过程
    lines = problematic_content.split('\n')
    clean_lines = []
    
    for line in lines:
        line = line.strip()
        
        # 跳过标题行和空行
        if line.startswith('#') or not line:
            continue
            
        # 处理过长的行
        if len(line) > 200:
            sentences = re.split(r'([。！？])', line)
            for i in range(0, len(sentences), 2):
                if i + 1 < len(sentences):
                    sentence = sentences[i] + sentences[i + 1]
                    sentence = sentence.strip()
                    if sentence and len(sentence) > 20:
                        clean_lines.append(sentence)
        else:
            if any(keyword in line for keyword in ['亿元', '%', '比率', '增长', '下降', '提升', '占比', '万元']):
                clean_lines.append(line)
    
    # 组成段落
    paragraphs = []
    current_paragraph = []
    
    for line in clean_lines:
        current_paragraph.append(line)
        if len(current_paragraph) >= 3:
            paragraphs.append(' '.join(current_paragraph))
            current_paragraph = []
    
    if current_paragraph:
        paragraphs.append(' '.join(current_paragraph))
    
    clean_content = '\n\n'.join(paragraphs)
    
    print(f"\n清理后内容:")
    print("-" * 60)
    print(clean_content)
    print("-" * 60)
    print(f"清理后行数: {len(clean_content.split(chr(10)))}")
    print(f"段落数: {len(paragraphs)}")
    
    # 保存测试结果
    with open('clean_extract_test_result.md', 'w', encoding='utf-8') as f:
        f.write(clean_content)
    
    print(f"\n📁 清理结果已保存到: clean_extract_test_result.md")
    
    return len(paragraphs) > 0

def main():
    """主函数"""
    print("🚀 分析extract_relevant_content格式问题")
    print("=" * 60)
    print("🎯 目标: 找出格式问题的根源并提供解决方案")
    print("=" * 60)
    
    # 分析当前的内容提取问题
    analyze_extract_content_issue()
    
    # 创建解决方案
    solution_code = create_clean_extract_solution()
    
    # 测试清理方案
    test_success = test_clean_extract()
    
    print(f"\n📋 问题分析总结:")
    print(f"  🔍 问题根源: extract_relevant_content提取的内容自带格式问题")
    print(f"  📝 主要问题: 长行、标题混乱、段落不分隔")
    print(f"  💡 解决方案: 在提取时就清理格式，只保留关键信息")
    print(f"  🧪 测试结果: {'✅ 成功' if test_success else '❌ 需改进'}")
    
    if test_success:
        print(f"\n🎯 下一步:")
        print(f"  1. 替换现有的extract_relevant_content方法")
        print(f"  2. 实施clean_extracted_content清理逻辑")
        print(f"  3. 测试新的章节生成效果")
        print(f"  4. 验证格式问题是否彻底解决")

if __name__ == "__main__":
    main()
