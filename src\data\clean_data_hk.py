import pandas as pd
from typing import Optional


def process_balance_data(df1: Optional[pd.DataFrame], df2: Optional[pd.DataFrame],
                           stock_code: str = "00020",
                           market: str = "HK",
                           period: str = "年度",
                           company_name: str = None,
                           save_dir: str = "."):
    """
    处理金融数据并保存为不同类别的 CSV 文件。

    参数:
    df1 (Optional[pd.DataFrame]): 包含资产负债表信息的 DataFrame。
    df2 (Optional[pd.DataFrame]): 包含分析指标信息的 DataFrame。
    stock_code (str): 股票代码，默认为 "00020"。
    market (str): 市场，默认为 "HK"。
    period (str): 周期，默认为 "年度"。
    company_name (str): 公司名称，默认为 None。
    save_dir (str): 保存文件的目录，默认为 "."。
    """
    if company_name is None:
        # 如果公司名称未提供，从文件路径中提取
        file_name = df1 if isinstance(df1, str) else ""
        if file_name:
            company_name = file_name.split('_')[0]

    # 从 df1 的 REPORT_DATE 列提取年份信息
    df1['year'] = pd.to_datetime(df1['REPORT_DATE']).dt.year

    # 从 df2 的 REPORT_DATE 列提取年份信息
    df2['year'] = pd.to_datetime(df2['REPORT_DATE']).dt.year

    # 定义需要的项目名称
    item_names = ['流动资产合计', '非流动资产合计', '总资产', '无形资产', '长期应收款', '物业厂房及设备', '指定以公允价值计量且其变动计入当期损益的金融资产', '现金及现金等价物', '存货']

    # 检查指定项目名称是否存在于 STD_ITEM_NAME 中
    missing_items = [item for item in ['指定以公允价值计量且其变动计入当期损益的金融资产', '现金及现金等价物'] if item not in df1['STD_ITEM_NAME'].unique()]

    # 如果有缺失项目名称，则从 item_names 中移除
    if missing_items:
        item_names = [item for item in item_names if item not in missing_items]

    # 从 df1 中筛选出需要的项目名称的数据
    filtered_df1 = df1[df1['STD_ITEM_NAME'].isin(item_names)]

    # 按年份和标准项目名称分组，对金额进行求和
    grouped_df1 = filtered_df1.groupby(['year', 'STD_ITEM_NAME'])['AMOUNT'].sum().unstack(fill_value=0)

    # 从 df2 中选取年份、资产负债率、流动比率列
    selected_df2 = df2[['year', 'DEBT_ASSET_RATIO', 'CURRENT_RATIO']]

    # 设置年份列为索引
    selected_df2.set_index('year', inplace=True)

    # 对 selected_df2 按年份列进行去重，保留最后一行
    selected_df2 = selected_df2[~selected_df2.index.duplicated(keep='last')]

    # 合并两个数据
    merged_df = pd.concat([grouped_df1, selected_df2], axis=1)

    # 构建保存文件的路径
    first_file_path = f"{save_dir}/{company_name}_{market}_balance_{stock_code}_第一类数据_{period}.csv"
    second_file_path = f"{save_dir}/{company_name}_{market}_balance_{stock_code}_第二类数据_{period}.csv"
    third_file_path = f"{save_dir}/{company_name}_{market}_balance_{stock_code}_第三类数据_{period}.csv"

    # 提取第 1 类数据并保存为 CSV 文件
    first_category = merged_df[['流动资产合计', '非流动资产合计', '总资产']]
    first_category.to_csv(first_file_path, index_label='年份')

    # 提取第 2 类数据并保存为 CSV 文件
    second_category = merged_df[[col for col in ['无形资产', '长期应收款', '物业厂房及设备', '指定以公允价值计量且其变动计入当期损益的金融资产', '现金及现金等价物', '存货'] if col not in missing_items]]
    second_category.to_csv(second_file_path, index_label='年份')

    # 提取第 3 类数据并保存为 CSV 文件
    third_category = merged_df[['DEBT_ASSET_RATIO', 'CURRENT_RATIO']]
    third_category.rename(columns={'DEBT_ASSET_RATIO': '资产负债率', 'CURRENT_RATIO': '流动比率'}).to_csv(third_file_path, index_label='年份')


def process_income_data(df1: Optional[pd.DataFrame], df2: Optional[pd.DataFrame],
                           stock_code: str = "00020",
                           market: str = "HK",
                           period: str = "年度",
                           company_name: str = None,
                           save_dir: str = ".") -> dict[str, str]:
    """
    处理财务数据，生成四类清洗后的表格并保存为CSV文件，第三张表包含销售及分销费用和研发相关费用（匹配含研发的字段）
    
    参数:
        df1: 收入表数据DataFrame
        df2: 分析指标数据DataFrame
        stock_code: 股票代码
        market: 市场标识
        period: 数据周期
        company_name: 公司名称
        save_dir: 保存目录
        
    返回:
        包含有效CSV文件路径的字典
    """
    # 验证输入数据
    if df1 is None or df2 is None:
        raise ValueError("输入的DataFrame不能为空")
    
    # 处理日期，提取年份
    df1['年份'] = pd.to_datetime(df1['REPORT_DATE']).dt.year
    df2['年份'] = pd.to_datetime(df2['REPORT_DATE']).dt.year
    
    # 获取公司名称（如果未提供）
    if not company_name and 'SECURITY_NAME_ABBR' in df1.columns:
        company_name = df1['SECURITY_NAME_ABBR'].iloc[0] or "unknown_company"
    
    # 定义英文字段到中文的映射（仅转换英文）
    english_to_chinese = {
        'GROSS_PROFIT_RATIO': '毛利率',
        'NET_PROFIT_RATIO': '净利率',
        'ROE_AVG': 'ROE_AVG',
        'ROE_YEARLY': 'ROE_YEARLY'
    }
    
    # 存储有效文件路径的字典
    valid_files = {}
    
    # ----------------------
    # 1. 收入成本利润数据（第一类）
    # ----------------------
    first_category = {
        '目标字段': ['营业收入', '销售成本', '毛利润', '归母股东收益'],
        '可能的字段': [
            ['营业收入', '营业额'],  # 营业收入的可能表述
            ['销售成本'],             # 销售成本的可能表述
            ['毛利润', '毛利'],       # 毛利润的可能表述
            ['归母股东收益', '应占溢利/(亏损) - 本公司拥有人']  # 归母股东收益的可能表述
        ]
    }
    
    # 查找第一类数据中存在的字段
    first_mapping = {}
    for target, candidates in zip(first_category['目标字段'], first_category['可能的字段']):
        for candidate in candidates:
            if candidate in df1['STD_ITEM_NAME'].unique():
                first_mapping[target] = candidate
                break
    
    if first_mapping:
        # 筛选数据
        first_data = df1[df1['STD_ITEM_NAME'].isin(first_mapping.values())][
            ['年份', 'STD_ITEM_NAME', 'AMOUNT']
        ]
        
        # 转换为宽表
        first_wide = first_data.pivot_table(
            index='年份',
            columns='STD_ITEM_NAME',
            values='AMOUNT'
        ).reset_index()
        
        # 重命名为目标字段
        first_rename = {v: k for k, v in first_mapping.items()}
        first_wide = first_wide.rename(columns=first_rename)
        
        # 保存文件
        first_path = f"{save_dir}/{company_name}_{market}_income_{stock_code}_第一类数据_{period}.csv"
        first_wide.to_csv(first_path, index=False)
        valid_files['第一类数据'] = first_path
    
    # ----------------------
    # 2. 利润率分析数据（第二类）
    # ----------------------
    second_category = ['GROSS_PROFIT_RATIO', 'NET_PROFIT_RATIO', 'ROE_AVG', 'ROE_YEARLY']
    second_available = [col for col in second_category if col in df2.columns]
    
    if second_available:
        # 筛选数据
        second_data = df2[['年份'] + second_available].copy()
        
        # 转换英文字段为中文
        second_rename = {col: english_to_chinese[col] for col in second_available}
        second_data = second_data.rename(columns=second_rename)
        
        # 合并ROE字段（如果有多个）
        if 'ROE' in second_data.columns and second_data.columns.tolist().count('ROE') > 1:
            roe_cols = [col for col in second_data.columns if col == 'ROE']
            second_data['ROE'] = second_data[roe_cols].mean(axis=1)
            second_data = second_data.drop(columns=roe_cols[1:])
        
        # 保存文件
        second_path = f"{save_dir}/{company_name}_{market}_income_{stock_code}_第二类数据_{period}.csv"
        second_data.to_csv(second_path, index=False)
        valid_files['第二类数据'] = second_path
    
    # ----------------------
    # 3. 成本相关数据（第三类）
    # 包含：销售成本、销售及分销费用、行政开支、研发开支（匹配含研发的字段）
    # ----------------------
    third_category = {
        '目标字段': ['销售成本', '销售及分销费用', '行政开支', '研发开支'],
        '可能的字段': [
            ['销售成本'],                     # 销售成本的可能表述
            ['销售及分销费用', '销售及分销开支'], # 销售及分销费用的可能表述
            ['行政开支'],                     # 行政开支的可能表述
            []  # 研发开支的可能表述将动态查找包含"研发"的字段
        ]
    }
    
    # 动态查找所有包含"研发"的字段
    rd_fields = [field for field in df1['STD_ITEM_NAME'].unique() if '研发' in field]
    third_category['可能的字段'][3] = rd_fields  # 将找到的研发相关字段加入
    
    # 查找第三类数据中存在的字段
    third_mapping = {}
    for target, candidates in zip(third_category['目标字段'], third_category['可能的字段']):
        if candidates:  # 只处理有候选字段的目标
            for candidate in candidates:
                if candidate in df1['STD_ITEM_NAME'].unique():
                    third_mapping[target] = candidate
                    break
    
    if third_mapping:
        # 筛选数据
        third_data = df1[df1['STD_ITEM_NAME'].isin(third_mapping.values())][
            ['年份', 'STD_ITEM_NAME', 'AMOUNT']
        ]
        
        # 转换为宽表
        third_wide = third_data.pivot_table(
            index='年份',
            columns='STD_ITEM_NAME',
            values='AMOUNT'
        ).reset_index()
        
        # 重命名为目标字段（将研发相关字段统一命名为"研发开支"）
        third_rename = {v: k for k, v in third_mapping.items()}
        third_wide = third_wide.rename(columns=third_rename)
        
        # 保存文件
        third_path = f"{save_dir}/{company_name}_{market}_income_{stock_code}_第三类数据_{period}.csv"
        third_wide.to_csv(third_path, index=False)
        valid_files['第三类数据'] = third_path
    
    # ----------------------
    # 4. 研发相关数据（第四类）
    # 包含：研发开支、研发开支同比增长率、研发开支占收入比
    # ----------------------
    # 获取研发开支字段（来自第三类数据的映射）
    rd_field = third_mapping.get('研发开支') if third_mapping else None
    # 获取营业收入字段（来自第一类数据的映射）
    revenue_field = first_mapping.get('营业收入') if first_mapping else None
    
    # 如果没有找到，尝试直接查找
    if not rd_field:
        rd_fields = [field for field in df1['STD_ITEM_NAME'].unique() if '研发' in field]
        rd_field = rd_fields[0] if rd_fields else None
    
    if not revenue_field:
        revenue_candidates = ['营业收入', '营业额']
        for candidate in revenue_candidates:
            if candidate in df1['STD_ITEM_NAME'].unique():
                revenue_field = candidate
                break
    
    if rd_field and revenue_field:
        # 筛选数据
        rd_data = df1[df1['STD_ITEM_NAME'].isin([rd_field, revenue_field])][
            ['年份', 'STD_ITEM_NAME', 'AMOUNT']
        ]
        
        # 转换为宽表
        rd_wide = rd_data.pivot_table(
            index='年份',
            columns='STD_ITEM_NAME',
            values='AMOUNT'
        ).reset_index()
        
        # 重命名字段
        rd_rename = {
            rd_field: '研发开支',
            revenue_field: '营业收入'
        }
        rd_wide = rd_wide.rename(columns=rd_rename)
        
        # 计算同比增长率和占收入比
        rd_wide['研发开支同比增长率'] = rd_wide['研发开支'].pct_change() * 100
        rd_wide['研发开支占收入比'] = (rd_wide['研发开支'] / rd_wide['营业收入']) * 100
        
        # 保留需要的字段
        rd_wide = rd_wide[['年份', '研发开支', '研发开支同比增长率', '研发开支占收入比']]
        
        # 保存文件
        fourth_path = f"{save_dir}/{company_name}_{market}_income_{stock_code}_第四类数据_{period}.csv"
        rd_wide.to_csv(fourth_path, index=False)
        valid_files['第四类数据'] = fourth_path
    
    return valid_files


def process_cash_data(df1: Optional[pd.DataFrame], df2: Optional[pd.DataFrame],
                           stock_code: str = "00020",
                           market: str = "HK",
                           period: str = "年度",
                           company_name: str = None,
                           save_dir: str = "."):
    if df1 is None or df2 is None:
        raise ValueError("df1 和 df2 都必须提供有效的 DataFrame")
    if company_name is None:
        raise ValueError("company_name 不能为 None")

    target_items = ['经营业务现金净额', '融资业务现金净额', '期末现金', '除税前溢利(业务利润)']

    filtered_df2 = df2[df2['STD_ITEM_NAME'].isin(target_items)]
    filtered_df2['REPORT_DATE'] = pd.to_datetime(filtered_df2['REPORT_DATE'])
    filtered_df2['Year'] = filtered_df2['REPORT_DATE'].dt.year

    pivot_df = filtered_df2.pivot_table(index='Year', columns='STD_ITEM_NAME', values='AMOUNT', aggfunc='first').reset_index()

    existing_columns = [col for col in target_items if col in pivot_df.columns]
    pivot_df = pivot_df[['Year'] + existing_columns]

    pivot_df.rename(columns={col: col for col in existing_columns}, inplace=True)

    first_file_path = f"{save_dir}/{company_name}_{market}_cash_{stock_code}_第一类数据_{period}.csv"
    pivot_df.to_csv(first_file_path, index=False)

    return first_file_path

if __name__ == "__main__":
    print("开始执行数据清理脚本...")

    # 读取资产负债表数据
    print("读取资产负债表数据...")
    df2 = pd.read_csv("D:/AiAgent/investment_agent/download_financial_statement_files/商汤科技_HK_00020_cash_flow_statement_年度.csv")
    print(f"资产负债表数据形状: {df2.shape}")

    # 读取分析指标数据
    print("读取分析指标数据...")
    df1 = pd.read_csv("D:/AiAgent/investment_agent/download_financial_statement_files/商汤科技_HK_00020_analysis_indicator_年度.csv")
    print(f"分析指标数据形状: {df1.shape}")

    # 处理并保存数据
    print("开始处理数据...")
    process_cash_data(df1, df2, stock_code="00020", market="HK", period="年度", company_name="商汤", save_dir=".")
    print("数据处理完成！")