# -*- coding: utf-8 -*-
"""
LLM配置类
"""

import os
from typing import Optional
from dataclasses import dataclass


@dataclass
class LLMConfig:
    """LLM配置类"""
    
    # 基本配置
    model: str = "doubao-seed-1-6-250615"
    api_key: str = "25e77c63-8424-4b59-b03d-a7facdb8ebf1"
    base_url: str = "https://ark.cn-beijing.volces.com/api/v3"
    
    # 生成参数
    max_tokens: int = 4096
    temperature: float = 0.7
    
    # 备用配置（可选）
    fallback_api_key: Optional[str] = None
    fallback_base_url: Optional[str] = None
    fallback_model: Optional[str] = None
    
    def __post_init__(self):
        """初始化后处理，从环境变量读取配置"""
        if not self.api_key:
            self.api_key = os.getenv("OPENAI_API_KEY", "")
        
        if not self.base_url or self.base_url == "https://api.openai.com/v1":
            env_base_url = os.getenv("OPENAI_BASE_URL")
            if env_base_url:
                self.base_url = env_base_url

        if not self.model or self.model == "gpt-3.5-turbo":
            env_model = os.getenv("OPENAI_MODEL")
            if env_model:
                self.model = env_model
        
        # 备用配置
        if not self.fallback_api_key:
            self.fallback_api_key = os.getenv("FALLBACK_API_KEY")
        
        if not self.fallback_base_url:
            self.fallback_base_url = os.getenv("FALLBACK_BASE_URL")
        
        if not self.fallback_model:
            self.fallback_model = os.getenv("FALLBACK_MODEL")
    
    def validate(self) -> bool:
        """验证配置是否有效"""
        if not self.api_key:
            print("⚠️ 警告: 未设置 API Key")
            return False
        
        if not self.base_url:
            print("⚠️ 警告: 未设置 Base URL")
            return False
        
        if not self.model:
            print("⚠️ 警告: 未设置模型名称")
            return False
        
        return True
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "model": self.model,
            "api_key": self.api_key,
            "base_url": self.base_url,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "fallback_api_key": self.fallback_api_key,
            "fallback_base_url": self.fallback_base_url,
            "fallback_model": self.fallback_model
        }
    
    @classmethod
    def from_dict(cls, config_dict: dict) -> "LLMConfig":
        """从字典创建配置"""
        return cls(**config_dict)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"LLMConfig(model={self.model}, base_url={self.base_url}, max_tokens={self.max_tokens})"
