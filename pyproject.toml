[project]
name = "investment-agent"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiofiles>=23.1.0",
    "akshare>=1.12.0",
    "beautifulsoup4>=4.9.0",
    "duckdb>=0.8.0",
    "duckduckgo-search>=3.0.0",
    "efinance>=0.5.0",
    "ipython>=7.0.0",
    "k-sogou-search>=0.0.2",
    "langchain-openai>=0.3.27",
    "langgraph>=0.5.1",
    "lxml>=4.6.0",
    "markdown>=3.4.0",
    "matplotlib>=3.5.0",
    "mdformat>=0.7.22",
    "numpy>=1.21.0",
    "openai>=1.0.0",
    "pandas>=2.0.0",
    "pillow>=8.0.0",
    "python-docx>=0.8.11",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0",
    "requests>=2.25.0",
    "scikit-learn>=1.7.0",
    "seaborn>=0.12.0",
    "tavily-python>=0.7.9",
]
[[index]]
url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/"
default = true 
