"""
整合的金融研报生成器
包含数据采集、分析和深度研报生成的完整流程
- 第一阶段：数据采集与基础分析
- 第二阶段：深度研报生成与格式化输出
"""

import os
import glob
import time
import json
import yaml
import re
import shutil
import requests
from datetime import datetime
from dotenv import load_dotenv
from urllib.parse import urlparse

# 导入数据分析相关模块
from agent.data_analysis_agent.util.data_analysis_agent import DataAnalysisWorkflow
from agent.data_analysis_agent.config.llm_config import LLMConfig
from agent.data_analysis_agent.tool.llm_helper import LLMHelper
from data.clean_data_hk import process_balance_data, process_cash_data, process_income_data
from data.get_shareholder_info import get_shareholder_info, get_table_content

# from src.utils.identify_competitors import identify_competitors_with_ai
from data.get_stock_intro import get_stock_intro, save_stock_intro_to_txt
from data.search_engine import SearchEngine

from data.get_data_statement import  get_all_financial_statements, save_financial_statements_to_csv,get_financial_analysis_indicator, save_financial_analysis_indicator_to_csv

class IntegratedResearchReportGenerator:
    """整合的研报生成器类"""
    
    def __init__(self, target_company="商汤科技", target_company_code="00020", target_company_market="HK", search_engine="ddg"):
        # 环境变量与全局配置
        load_dotenv()
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
        self.model = os.getenv("OPENAI_MODEL", "gpt-4")

        self.api_bot_key = os.getenv("OPENAI_API_BOT_KEY")
        self.base_bot_url = os.getenv("OPENAI_BASE_BOT_URL", "https://api.openai.com/v1")
        self.bot_model = os.getenv("OPENAI_BOT_MODEL", "gpt-4")

        # 打印模型
        print(f"🔧 使用的模型: {self.model}")
        self.target_company = target_company
        self.target_company_code = target_company_code
        self.target_company_market = target_company_market
        
        # 搜索引擎配置
        self.search_engine = SearchEngine("tavily")
        print(f"🔍 搜索引擎已配置为: {search_engine.upper()}")
        
        # 目录配置
        self.data_dir = "./download_financial_statement_files"
        self.company_info_dir = "./company_info"
        self.industry_info_dir = "./industry_info"

        # 创建必要的目录
        for dir_path in [self.data_dir, self.company_info_dir, self.industry_info_dir]:
            os.makedirs(dir_path, exist_ok=True)
        
        # LLM配置
        self.llm_config = LLMConfig(
            api_key=self.api_key,
            base_url=self.base_url,
            model=self.model,
            temperature=0.6
        )

        self.llm_bot_config = LLMConfig(
            api_key=self.api_bot_key,
            base_url=self.base_bot_url,
            model = self.bot_model,
            temperature=0.4,
        )

        self.llm = LLMHelper(self.llm_bot_config)
        
        # 存储分析结果
        self.analysis_results = {}
    
    def stage1_data_collection(self):
        """第一阶段：数据采集与基础分析"""
        print("\n" + "="*80)
        print("🚀 开始第一阶段：数据采集与基础分析")
        print("="*80)
        
        # 2. 获取目标公司财务数据
        print(f"\n📊 获取目标公司 {self.target_company} 的财务数据...")
        target_financials = get_all_financial_statements(
            stock_code=self.target_company_code,
            market=self.target_company_market,
            period="年度",
            verbose=False
        )
        save_financial_statements_to_csv(
            financial_statements=target_financials,
            stock_code=self.target_company_code,
            market=self.target_company_market,
            company_name=self.target_company,
            period="年度",
            save_dir=self.data_dir
        )

        target_analysis = get_financial_analysis_indicator(
            stock_code=self.target_company_code,
            market=self.target_company_market,
            period="年度",
            verbose=False
        )
        save_financial_analysis_indicator_to_csv(
            df_analysis=target_analysis,
            stock_code=self.target_company_code,
            market=self.target_company_market,
            period="年度",
            save_dir=self.data_dir,
            company_name=self.target_company
        )
        
        process_cash_data(target_analysis,target_financials["cash_flow_statement"], self.target_company_code, self.target_company_market, "年度", self.target_company, self.data_dir)
        
        process_balance_data(target_financials["balance_sheet"], target_analysis, self.target_company_code, self.target_company_market, "年度", self.target_company, self.data_dir)
        
        process_income_data(target_financials["income_statement"], target_analysis, self.target_company_code, self.target_company_market, "年度", self.target_company, self.data_dir)
        
        # 3. 获取公司基础信息
        print("\n🏢 获取公司基础信息...")
        all_base_info_targets = [(self.target_company, self.target_company_code, self.target_company_market)]
        
        
        for company_name, company_code, market in all_base_info_targets:
            print(f"  获取 {company_name}({market}:{company_code}) 的基础信息")
            company_info = get_stock_intro(company_code, market=market)
            if company_info:
                save_path = os.path.join(self.company_info_dir, f"{company_name}_{market}_{company_code}_info.txt")
                save_stock_intro_to_txt(company_code, market, save_path)
                print(f"    信息已保存到: {save_path}")
            else:
                print(f"    未能获取到 {company_name} 的基础信息")
            time.sleep(1)
        

        
        # 6. 运行财务分析
        print("\n📈 运行财务分析...")

        # 使用DataAnalysisWorkflow进行数据分析

        # 目标公司估值与预测分析
        ## 资产负债get_balance_sheet
        balance_files = self.get_sensetime_files(self.target_company_code, self.data_dir,"balance")
        balance_valuation_report = None
        if balance_files:
            query = f"专注于并且只分析{self.target_company}债务报表相关，构建估值与预测模型，按年份分析关键数据变化对财务结果的影响，分析负债率、流动比例等数值,挖掘需要分析的数据。绘制相关图和表。最后生成汇报给我。资产负债可能需要分析但不限于需要分析的数据包括：\
   - 资产结构（流动/非流动）和质量分析 \
   - 负债结构和财务风险评估 \
   - 资产负债率、流动比率、速动比率等偿债能力指标 \
   - 存货周转率、应收账款周转率等运营效率指标 \
   - 资产堆积图，分析各类资产所占比例，比如现金等价物、物业、厂房设备、按公允价值计量等各类资产的资产堆积图 \
   - 财务风险、营运能力、应收贸易款等"
            balance_valuation_report = self.analyze_company_valuation(balance_files, self.llm_config, query)
            

        ## 利润表income_statement
        income_files = self.get_sensetime_files(self.target_company_code, self.data_dir,"income")
        income_valuation_report = None
        if income_files:
            query = f"专注于并且只分析{self.target_company}利润表相关，构建估值与预测模型，按年份分析关键数据变化对利润结果的影响，分析收入成本利润、利润率图、收入成本构成等数值，挖掘需要分析的数据。绘制相关图和表。最后生成汇报给我。利润表可能需要分析但不限于需要分析的数据包括： \
   - 期间费用率：营业成本、销售费用、管理费用、研发费用等结构分析 \
   - 费用率变化趋势和同比分析 \
   - 费用效率和控制能力评估 \
   - 毛利率、净利率、ROE趋势变化 \
   - 研发开支数额变化、同比增长率、占收入比率 \
   - 每年收入成本构成，如销售成本，行政费用率，研发费用率，经营活动等"
            income_valuation_report = self.analyze_company_valuation(income_files, self.llm_config, query)

        ## 现金流表cash_flow_statement
        cash_files = self.get_sensetime_files(self.target_company_code, self.data_dir,"cash")
        cash_valuation_report = None
        if cash_files:
            query = f"分析{self.target_company}现金流表，构建估值与预测模型，按年份分析关键数据变化以及现金流情况，分析各类现金流净额变化等数值。绘制相关图和表。最后生成汇报给我。现金流分析可能需要分析但不限于需要分析的数据包括：\
   - 经营活动、投资活动、筹资活动现金流分析 \
   - 现金流质量和可持续性评估 \
   - 现金流与利润的匹配度分析"
            cash_valuation_report = self.analyze_company_valuation(cash_files, self.llm_config, query)
        
        # 7. 整理所有分析结果
        print("\n📋 整理分析结果...")
        
        # 整理公司信息
        company_infos = self.get_company_infos(self.company_info_dir)
        company_infos = self.llm.call(
            f"请整理以下公司信息内容，确保格式清晰易读，并保留关键信息：\n{company_infos}",
            system_prompt="你是一个专业的公司信息整理师。",
            temperature=0.5
        )
        
        # 整理股权信息
        try:
            info = get_shareholder_info()
            shareholder_info = info.get("tables") if info else None
            if shareholder_info:
                table_content = get_table_content(shareholder_info)
                shareholder_analysis = self.llm.call(
                    f"请分析以下{self.target_company}股东信息表格内容：\n" + table_content,
                    system_prompt="你是一个专业的股东信息分析师。",
                    temperature=0.3
                )
            else:
                shareholder_analysis = f"未能获取到{self.target_company}的股东信息"
        except Exception as e:
            print(f"获取股东信息时出错: {e}")
            shareholder_analysis = f"获取{self.target_company}股东信息时出现错误"
        
                # 5. 搜索行业信息
        print("\n🔍 搜索行业信息...")
        all_search_results = {}
          # 搜索目标公司行业信息
        target_search_keywords = f"{self.target_company} 市场份额 竞争分析 业务模式"
        print(f"  正在搜索: {target_search_keywords}")
        # 进行目标公司搜索
        target_results = self.search_engine.search(target_search_keywords, 10)
        all_search_results[self.target_company] = target_results
        
        # 保存搜索结果
        search_results_file = os.path.join(self.industry_info_dir, "all_search_results.json")
        try:
            with open(search_results_file, 'w', encoding='utf-8') as f:
                json.dump(all_search_results, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存搜索结果时出错: {e}")
            search_results_file = None
        
        # 整理行业信息搜索结果
        search_res = ""
        if search_results_file and os.path.exists(search_results_file):
            try:
                with open(search_results_file, 'r', encoding='utf-8') as f:
                    all_search_results = json.load(f)
                for company, results in all_search_results.items():
                    search_res += f"【{company}搜索信息开始】\n"
                    for result in results:
                        search_res += f"标题: {result.get('title', '无标题')}\n"
                        search_res += f"链接: {result.get('url', '无链接')}\n"  # 修正：从 'href' 改为 'url'
                        search_res += f"摘要: {result.get('description', '无摘要')}\n"  # 修正：从 'body' 改为 'description'
                        search_res += "----\n"
                    search_res += f"【{company}搜索信息结束】\n\n"
            except Exception as e:
                print(f"读取搜索结果文件时出错: {e}")
                search_res = f"无法读取{self.target_company}的搜索结果"
        else:
            search_res = f"搜索结果文件不存在，无法获取{self.target_company}的搜索信息"

        search_analysis = self.llm.call(
                f"请分析公司{self.target_company}业务营运方面，保证报告非常完善和具体，不仅仅只有总结还有具体数据支撑，一定要非常非常非常详细，报告内容尽可能多，请利用包括但不限于以下数据完成报告，尽量充分，避免简写。数据如下：\n <data>" + search_res + "\n</data>",
                system_prompt=f"您是一名专业的战略分析师，专注于业务运营和竞争情报领域。您的专业能力包括：\
-核心职责：\
分析{self.target_company}公司竞争定位与战略部署\
评估行业地位与市场定位\
研判核心业务运营与商业模式\
开展基于数据的财务与运营分析\
-分析框架：\
分析公司时，需提供结构化见解，涵盖以下方面：\
战略定位：当前战略、战略举措、市场定位\
核心业务运营：主要收入来源、运营效率、关键业务板块\
-需要标准引用",
                temperature=0.5
            )

        #行业简析
        industry_search_keywords = f"{self.target_company} 所在行业情况 行业地位 国家政策"
        print(f"  正在搜索: {industry_search_keywords}")
        # 进行目标公司搜索
        industry_results = self.search_engine.search(industry_search_keywords, 10)
        
        # 构建行业搜索结果字符串
        industry_search_res = ""
        if industry_results:
            for result in industry_results:
                industry_search_res += f"标题: {result.get('title', '无标题')}\n"
                industry_search_res += f"链接: {result.get('url', '无链接')}\n"
                industry_search_res += f"摘要: {result.get('description', '无摘要')}\n"
                industry_search_res += "----\n"

        industry_analysis = self.llm.call(
                f"请分析公司{self.target_company}所在行业前景，保证报告非常完善和具体，不仅仅只有总结还有具体数据支撑，一定要非常非常非常详细，报告内容尽可能多，请利用包括但不限于以下数据完成报告，尽量充分，避免简写。数据如下：\n <data>" + industry_search_res+ "\n</data>",  # 保持原代码逻辑
                system_prompt=f"请为 {self.target_company} 公司开展全面的行业分析,并且尽可能详细，重点关注以下关键领域：\
-行业前景分析： \
分析 {self.target_company} 所在行业的现状与未来前景 \
识别行业关键趋势、增长驱动因素及潜在挑战 \
评估市场规模、增长率和竞争动态 \
评估影响该行业的监管环境和技术变革 \
-需要标准引用",
                temperature=0.5
            )
        
        #行业竞争对手分析
        competitor_search_keywords = f"{self.target_company} 竞争对手 行业地位 竞争优势"
        print(f"  正在搜索: {competitor_search_keywords}")
        # 进行目标公司搜索
        competitor_results = self.search_engine.search(competitor_search_keywords, 10)
        
        # 构建行业搜索结果字符串
        competitor_search_res = ""
        if competitor_results:
            for result in competitor_results:
                competitor_search_res += f"标题: {result.get('title', '无标题')}\n"
                competitor_search_res += f"链接: {result.get('url', '无链接')}\n"
                competitor_search_res += f"摘要: {result.get('description', '无摘要')}\n"
                competitor_search_res += "----\n"

        competitor_analysis = self.llm.call(
                f"请分析公司{self.target_company}所在行业竞争力，进行横向竞争分析，保证报告非常完善和具体，不仅仅只有总结还有具体数据支撑，一定要非常非常非常详细，报告内容尽可能多，请利用包括但不限于以下数据完成报告，尽量充分，避免简写。数据如下：\n<data>" + competitor_search_res+ "\n</data>",  # 保持原代码逻辑
                system_prompt=f"请为 {self.target_company} 公司开展全面的行业竞争分析,并且尽可能详细，重点关注以下关键领域：\
识别并分析同行业的主要竞争对手和同行公司 \
从多个维度将 {self.target_company} 与竞争对手进行比较：\
财务业绩（收入、盈利能力、增长率）\
市场份额和竞争定位 \
商业模式和战略重点 \
创新能力和研发投入。\
关键维度对比，比如财务表现，市场份额，技术能力，商业模式等。\
-需要标准引用",
                temperature=0.5
            )
        

        
        # 统一保存为markdown
        md_output_file = f"财务研报汇总_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(md_output_file, 'w', encoding='utf-8') as f:
            f.write(f"# 公司基础信息\n\n## 整理后公司信息\n\n{company_infos}\n\n")
            f.write(f"# 股权信息分析\n\n{shareholder_analysis}\n\n")
            f.write(f"# 公司行业前景\n\n{industry_analysis}\n\n")
            f.write(f"# 公司行业竞争对手\n\n{competitor_analysis}\n\n")
            f.write(f"# 公司业务营运信息\n\n{search_analysis}\n\n")
            if balance_valuation_report and isinstance(balance_valuation_report, dict):
                f.write(f"# {self.target_company}资产负债表分析\n\n{balance_valuation_report.get('final_report', '未生成报告')}\n\n")
            if income_valuation_report and isinstance(income_valuation_report, dict):
                f.write(f"# {self.target_company}利润表分析\n\n{income_valuation_report.get('final_report', '未生成报告')}\n\n")
            if cash_valuation_report and isinstance(cash_valuation_report, dict):
                f.write(f"# {self.target_company}现金流表分析\n\n{cash_valuation_report.get('final_report', '未生成报告')}\n\n")
        
        print(f"\n✅ 第一阶段完成！基础分析报告已保存到: {md_output_file}")
        
        # 存储结果供第二阶段使用
        self.analysis_results = {
            'md_file': md_output_file,
            'search_res': search_res,
            'balance_valuation_report': balance_valuation_report,
            'income_valuation_report': income_valuation_report,
            'cash_valuation_report': cash_valuation_report
        }
        
        return md_output_file
    
    def stage2_deep_report_generation(self, md_file_path):
        """第二阶段：深度研报生成"""
        print("\n" + "="*80)
        print("🚀 开始第二阶段：深度研报生成")
        print("="*80)
        
        # 处理图片路径
        print("🖼️ 处理图片路径...")
        new_md_path = md_file_path.replace('.md', '_images.md')
        images_dir = os.path.join(os.path.dirname(md_file_path), 'images')
        self.extract_images_from_markdown(md_file_path, images_dir, new_md_path)
        
        # 加载报告内容
        report_content = self.load_report_content(new_md_path)
        background = self.get_background()
        
        # 生成大纲
        print("\n📋 生成报告大纲...")
        parts = self.generate_outline(self.llm, background, report_content)
        
        # 分段生成深度研报
        print("\n✍️ 开始分段生成深度研报...")
        full_report = ['# 商汤科技公司研报\n']
        prev_content = ''

        for idx, part in enumerate(parts):
            part_title = part.get('part_title', f'部分{idx+1}')
            print(f"\n  正在生成：{part_title}")
            section_text = self.generate_section(
                self.llm, part_title, prev_content, background, report_content
            )
            full_report.append(section_text)
            print(f"  ✅ 已完成：{part_title}")
            prev_content = '\n'.join(full_report)

        # 添加统一的引用文献章节
        print("\n📚 添加引用文献...")
        references_section = self.generate_references_section()
        full_report.append(references_section)

        # 保存最终报告
        final_report = '\n\n'.join(full_report)
        output_file = f"深度财务研报分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        self.save_markdown(final_report, output_file)
        
        # 格式化和转换
        print("\n🎨 格式化报告...")
        self.format_markdown(output_file)
        
        print("\n📄 转换为Word文档...")
        self.convert_to_docx(output_file)
        
        print(f"\n✅ 第二阶段完成！深度研报已保存到: {output_file}")
        return output_file
    
    def run_full_pipeline(self):
        """运行完整流程"""
        print("\n" + "="*100)
        print("🎯 启动整合的金融研报生成流程")
        print("="*100)
        
        # 第一阶段：数据采集与基础分析
        md_file = self.stage1_data_collection()
        
        # # 第二阶段：深度研报生成
        final_report = self.stage2_deep_report_generation(md_file)
        
        print("\n" + "="*100)
        print("🎉 完整流程执行完毕！")
        print(f"📊 基础分析报告: {md_file}")
        print(f"📋 深度研报: {final_report}")
        print("="*100)

        return final_report

    # ========== 辅助方法（从原始脚本移植） ==========
    
    def get_company_infos(self, data_dir="./company_info"):
        """获取公司信息"""
        all_files = os.listdir(data_dir)
        company_infos = ""
        for file in all_files:
            if file.endswith(".txt"):
                company_name = file.split(".")[0]
                with open(os.path.join(data_dir, file), 'r', encoding='utf-8') as f:
                    content = f.read()
                company_infos += f"【公司信息开始】\n公司名称: {company_name}\n{content}\n【公司信息结束】\n\n"
        return company_infos
    
    def get_company_files(self, data_dir):
        """获取公司文件"""
        all_files = glob.glob(f"{data_dir}/*.csv")
        companies = {}
        for file in all_files:
            filename = os.path.basename(file)
            company_name = filename.split("_")[0]
            companies.setdefault(company_name, []).append(file)
        return companies
    
    def analyze_individual_company(self, company_name, files, llm_config, query=None, verbose=True):
        """分析单个公司"""
        if query is None:
            query = "基于表格的数据，分析有价值的内容，并绘制相关图表。最后生成汇报给我。"

        # 使用DataAnalysisWorkflow进行分析
        workflow = DataAnalysisWorkflow(llm_config)
        report = workflow.run_analysis(user_input=query, files=files)

        if verbose:
            print(f"✅ 完成 {company_name} 的数据分析")

        return report
    
    def format_final_reports(self, all_reports):
        """格式化最终报告"""
        formatted_output = []
        for company_name, report in all_reports.items():
            formatted_output.append(f"【{company_name}财务数据分析结果开始】")

            # DataAnalysisWorkflow返回的结果格式处理
            if isinstance(report, dict):
                # 尝试获取最终报告内容
                final_report = report.get("final_report", "")
                if not final_report:
                    # 如果没有final_report，尝试从messages中提取最后的分析结果
                    messages = report.get("messages", [])
                    if messages:
                        # 获取最后一条消息作为报告内容
                        last_message = messages[-1]
                        if hasattr(last_message, 'content'):
                            final_report = last_message.content
                        elif isinstance(last_message, dict):
                            final_report = last_message.get('content', '未生成报告')
                        else:
                            final_report = str(last_message)
                    else:
                        final_report = "未生成报告"

                # 添加分析过程信息
                session_dir = report.get("session_output_dir", "")
                total_rounds = report.get("total_rounds", 0)
                if session_dir or total_rounds:
                    formatted_output.append(f"分析会话目录: {session_dir}")
                    formatted_output.append(f"分析轮数: {total_rounds}")
                    formatted_output.append("---")

                formatted_output.append(final_report)
            else:
                # 如果不是字典格式，直接转换为字符串
                formatted_output.append(str(report))

            formatted_output.append(f"【{company_name}财务数据分析结果结束】")
            formatted_output.append("")
        return "\n".join(formatted_output)
    
    def analyze_companies_in_directory(self, data_directory, llm_config, query="基于表格的数据，分析有价值的内容，并绘制相关图表。最后生成汇报给我。"):
        """分析目录中的所有公司"""
        company_files = self.get_company_files(data_directory)
        all_reports = {}
        for company_name, files in company_files.items():
            print(f"📊 正在分析 {company_name} 的财务数据...")
            report = self.analyze_individual_company(company_name, files, llm_config, query, verbose=False)
            if report:
                all_reports[company_name] = report
        return all_reports

    
    
    
    def get_sensetime_files(self, target_company_code, data_dir, category):
        """获取目标公司的财务数据文件"""
        all_files = glob.glob(f"{data_dir}/*.csv")
        target_files = []
        print(f'所有文件：{all_files}')
        for file in all_files:
            try:
                filename = os.path.basename(file)
                parts = filename.split("_")
                if len(parts) >= 4:  # 确保文件名格式正确
                    company_code = parts[3]
                    category_name = parts[2]
                    if target_company_code in company_code and (category in category_name or "analysis" in category_name):
                        target_files.append(file)
                else:
                    print(f"警告：文件名格式不符合预期: {filename}")
            except Exception as e:
                print(f"处理文件 {file} 时出错: {e}")
        return target_files
    
    def analyze_company_valuation(self, files, llm_config, query):
        """分析目标公司的估值与预测"""
        if query is None:
            query = f"基于{self.target_company}三大表的数据，构建估值与预测模型，分析关键数据变化对财务结果的影响,并绘制相关图表。最后生成汇报给我。"

        # 使用DataAnalysisWorkflow进行分析
        workflow = DataAnalysisWorkflow(llm_config)
        report = workflow.run_analysis(user_input=query, files=files)

        return report
    
    # ========== 深度研报生成相关方法 ==========
    
    def load_report_content(self, md_path):
        """加载报告内容"""
        with open(md_path, "r", encoding="utf-8") as f:
            return f.read()
    
    def get_background(self):
        """获取背景信息"""
        return '''
本报告基于自动化采集与分析流程，涵盖如下环节：
- 公司基础信息等数据均通过akshare、公开年报、主流财经数据源自动采集。
- 财务三大报表数据来源：东方财富-港股-财务报表-三大报表 (https://emweb.securities.eastmoney.com/PC_HKF10/FinancialAnalysis/index)
- 主营业务信息来源：同花顺-主营介绍 (https://basic.10jqka.com.cn/new/000066/operate.html)
- 股东结构信息来源：同花顺-股东信息 (https://basic.10jqka.com.cn/HK0020/holder.html) 通过网页爬虫技术自动采集
- 行业信息通过DuckDuckGo等公开搜索引擎自动抓取，引用了权威新闻、研报、公司公告等。
- 财务分析、对比分析、估值与预测均由大模型（如GPT-4）自动生成，结合了行业对标、财务比率、治理结构等多维度内容。
- 相关数据与分析均在脚本自动化流程下完成，确保数据来源可追溯、分析逻辑透明。
- 详细引用与外部链接已在正文中标注。
- 数据接口说明与免责声明见文末。
'''
    
    def get_structured_outline(self):
        """获取结构化的报告大纲"""
        structured_outline = [
            {
                "part_title": "公司概览与基本面分析",
                "part_desc": "公司基础信息、主营业务、股权结构、行业地位等综合概览"
            },
            {
                "part_title": "行业分析与竞争环境",
                "part_desc": "所在行业前景、市场环境、竞争格局、政策影响等分析"
            },
            {
                "part_title": "财务状况综合分析",
                "part_desc": "基于三大财务报表的综合财务分析，包括盈利能力、偿债能力、运营效率等"
            },
            {
                "part_title": "资产负债分析",
                "part_desc": "资产结构变化、负债管理、偿债能力、财务风险评估"
            },
            {
                "part_title": "盈利能力与成本分析",
                "part_desc": "收入结构、成本控制、利润率变化、期间费用分析"
            },
            {
                "part_title": "现金流量分析",
                "part_desc": "经营、投资、筹资现金流分析，现金流质量评估"
            },
            {
                "part_title": "估值分析与投资建议",
                "part_desc": "基于财务分析的估值模型、投资价值评估、投资建议"
            },
            {
                "part_title": "风险提示与展望",
                "part_desc": "主要风险因素识别、未来发展展望、关注要点"
            }
        ]
        return structured_outline

    def generate_outline(self, llm, background, report_content):
        """生成大纲"""
        # 直接使用结构化大纲，避免YAML解析问题
        parts = self.get_structured_outline()

        print("\n===== 使用默认结构化大纲 =====\n")
        for i, part in enumerate(parts, 1):
            print(f"{i}. {part['part_title']}: {part['part_desc']}")

        return parts
    
    def extract_financial_tables(self, report_content):
        """从报告内容中提取财务数据，生成表格"""
        # 这里可以根据实际需要提取关键财务数据
        # 示例：提取一些关键指标
        sample_table = """
| 财务指标 | 2022年 | 2023年 | 2024年 | 变化趋势 |
|---------|--------|--------|--------|----------|
| 总资产(亿元) | 580 | 650 | 700 | ↗ |
| 营业收入(亿元) | 34.5 | 38.2 | 42.1 | ↗ |
| 净利润(亿元) | -12.8 | -8.5 | -5.2 | ↗ |
| 资产负债率(%) | 48 | 46 | 45 | ↘ |
| 现金及等价物(亿元) | 165 | 128 | 95 | ↘ |
"""
        return sample_table

    def get_available_images_from_report(self, report_content):
        """从财务研报汇总内容中提取实际存在的图片引用"""
        import re

        # 从报告内容中提取所有图片引用
        image_pattern = re.compile(r'!\[([^\]]*)\]\(([^)]+)\)')
        matches = image_pattern.findall(report_content)

        available_images = []
        for alt_text, img_path in matches:
            # 检查图片文件是否实际存在，尝试多个可能的路径
            possible_paths = [
                img_path,  # 原始路径
                f"./images/{os.path.basename(img_path)}",  # images目录
                f"images/{os.path.basename(img_path)}"  # 相对images目录
            ]

            found_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    found_path = path
                    break

            if found_path:
                available_images.append({
                    'alt_text': alt_text,
                    'path': found_path,
                    'original_path': img_path,
                    'filename': os.path.basename(img_path)
                })

        return available_images

    def get_section_content_mapping(self, report_content):
        """根据章节标题映射相关内容，避免重复"""
        # 获取实际存在的图片
        available_images = self.get_available_images_from_report(report_content)

        content_mapping = {
            "公司概览与基本面分析": {
                "keywords": ["公司基础信息", "整理后公司信息", "股权信息分析"],
                "focus": "公司基本情况、主营业务、股权结构等基础信息整合",
                "available_images": [img for img in available_images if any(keyword in img['alt_text'] or keyword in img['filename'] for keyword in ["股权", "结构", "组织"])]
            },
            "行业分析与竞争环境": {
                "keywords": ["公司行业前景", "公司业务营运信息", "公司行业竞争对手"],
                "focus": "行业发展趋势、竞争格局、市场地位分析",
                "available_images": [img for img in available_images if any(keyword in img['alt_text'] or keyword in img['filename'] for keyword in ["行业", "竞争", "市场"])]
            },
            "财务状况综合分析": {
                "keywords": ["财务指标相关性", "关键财务指标"],
                "focus": "基于三大报表的综合财务健康度评估",
                "available_images": [img for img in available_images if any(keyword in img['alt_text'] or keyword in img['filename'] for keyword in ["财务指标", "关键", "相关性"])]
            },
            "资产负债结构分析": {
                "keywords": ["资产负债表分析", "资产结构", "偿债能力", "资产组成"],
                "focus": "资产结构变化、负债管理、偿债能力专项分析",
                "available_images": [img for img in available_images if any(keyword in img['alt_text'] or keyword in img['filename'] for keyword in ["资产", "负债", "偿债", "结构"])]
            },
            "盈利能力与成本分析": {
                "keywords": ["利润表分析", "收入成本", "利润率", "期间费用"],
                "focus": "收入结构、成本控制、盈利能力专项分析",
                "available_images": [img for img in available_images if any(keyword in img['alt_text'] or keyword in img['filename'] for keyword in ["收入", "成本", "利润", "费用", "研发"])]
            },
            "现金流量分析": {
                "keywords": ["现金流表分析", "现金流", "三类现金流"],
                "focus": "现金流质量、流动性管理专项分析",
                "available_images": [img for img in available_images if any(keyword in img['alt_text'] or keyword in img['filename'] for keyword in ["现金流", "现金"])]
            },
            "估值分析与投资建议": {
                "keywords": ["估值", "预测", "投资建议"],
                "focus": "基于财务分析的估值评估和投资建议",
                "available_images": [img for img in available_images if any(keyword in img['alt_text'] or keyword in img['filename'] for keyword in ["估值", "预测", "趋势"])]
            },
            "风险提示与展望": {
                "keywords": ["风险", "挑战", "展望"],
                "focus": "主要风险识别和未来发展展望",
                "available_images": [img for img in available_images if any(keyword in img['alt_text'] or keyword in img['filename'] for keyword in ["风险", "分析"])]
            }
        }
        return content_mapping

    def generate_section(self, llm, part_title, prev_content, background, report_content):
        """生成章节"""
        # 获取内容映射，避免重复
        content_mapping = self.get_section_content_mapping(report_content)
        section_info = content_mapping.get(part_title, {})
        keywords = section_info.get("keywords", [])
        focus = section_info.get("focus", "")
        available_images = section_info.get("available_images", [])

        section_prompt = f"""
你是一位顶级金融分析师和研报撰写专家。请基于以下内容，直接输出\"{part_title}\"这一部分的完整研报内容。

**本章节重点：**
{focus}

**可用的图片（仅限引用以下实际存在的图片）：**
{chr(10).join([f"- {img['alt_text']}: {img['path']}" for img in available_images]) if available_images else '本章节无可用图片'}

**内容要求：**
1. 直接输出完整可用的研报内容，以\"## {part_title}\"开头
2. 重点关注以下关键词相关内容：{', '.join(keywords)}
3. **避免与前文重复**：仔细查看【已生成前文】，不要重复已经详细分析过的内容
4. **内容归类整合**：将相似的分析内容进行整合，形成逻辑清晰的章节
5. **必须包含图表分析**：根据章节内容，积极引用【财务研报汇总内容】中的相关图片和表格
6. **表格数据展示**：对于关键财务数据，使用markdown表格格式进行展示
7. **内容详细程度要求（重要）**：
   - 每个分析要点都要深入展开，提供具体的数字、比例、变化趋势
   - 对每个财务指标要进行多维度分析：绝对值、相对值、同比、环比、行业对比
   - 分析原因时要从多个角度解释：市场因素、公司战略、行业趋势、政策影响等
   - 每个结论都要有充分的数据支撑和逻辑推理过程
   - 避免简写和概括性表述，要具体详细地阐述每个观点
8. **分析深度要求**：
   - 不仅要说明"是什么"，更要深入分析"为什么"和"影响是什么"
   - 对关键指标要进行敏感性分析和情景分析
   - 要结合公司战略、行业特点、宏观环境进行综合分析
   - 每个章节内容不少于800字，确保分析的充分性
9. 在正文中引用数据、事实、图片等信息时，适当位置插入参考资料符号（如[1][2][3]）
10. **严禁在章节末尾添加任何引用文献、参考资料或数据来源列表**
11. **章节结束标志**：章节内容结束后直接结束，不要添加任何形如"[1] 东方财富..."的引用列表
12. **图片引用要求（务必严格遵守）：**
    - 只能引用【可用的图片】列表中明确列出的图片文件
    - 必须使用标准markdown格式：![图片描述](图片路径)
    - 必须使用列表中提供的完整路径，不得修改
    - 严禁引用不在【可用的图片】列表中的任何图片
    - 如果【可用的图片】列表为空，则不要引用任何图片
    - 禁止虚构、杜撰、改编、猜测图片地址
13. **表格数据要求（重要）：**
    - 必须积极引用【财务研报汇总内容】中的表格数据
    - 完整保留表格的markdown格式（| 列1 | 列2 | 列3 |）
    - 表格前后要有空行分隔
    - 可以基于表格数据进行深入分析和解读
    - 特别关注风险与机遇矩阵、商业模式对比等重要表格
14. 不要输出任何【xxx开始】【xxx结束】等分隔符
15. 不要输出\"建议补充\"、\"需要添加\"等提示性语言
16. 内容要详实、专业，可直接使用，绝对不要简写

**数据来源标注：**
- 财务数据标注：（数据来源：东方财富-港股-财务报表[1]）
- 主营业务信息标注：（数据来源：同花顺-主营介绍[2]）
- 股东结构信息标注：（数据来源：同花顺-股东信息网页爬虫[3]）
- 行业信息标注：（数据来源：搜索引擎[4]）

【本次任务】
{part_title}

【已生成前文】
{prev_content[-3000:] if len(prev_content) > 3000 else prev_content}

【背景说明开始】
{background}
【背景说明结束】

【财务研报汇总内容开始】
{report_content}
【财务研报汇总内容结束】
"""
        section_text = llm.call(
            section_prompt,
            system_prompt=f"""你是顶级金融分析师，专门生成完整可用的深度研报内容。

核心要求：
1. 本次任务重点：{focus}
2. 内容必须极其详细和全面，绝对不允许简写或概括
3. 每个分析要点都要深入展开，提供具体数字、详细推理过程
4. 每个章节内容不少于800字，确保分析的充分性和深度
5. 对每个财务指标要进行多维度详细分析：历史趋势、行业对比、影响因素、未来预期
6. 严格避免与前文重复，将相似内容进行整合归类
7. 输出必须是完整的研报正文，无需用户修改
8. 严格禁止输出分隔符、建议性语言或虚构内容
9. 只允许引用真实存在的图片地址
10. 绝对不要在章节末尾添加任何形式的引用文献、参考资料或数据来源列表

写作风格：专业、详细、深入、不简写、逻辑严密、数据丰富""",
            max_tokens=12288,  # 设置为模型支持的最大token限制
            temperature=0.3    # 降低温度以获得更稳定详细的输出
        )

        # 后处理：移除章节末尾的引用文献
        section_text = self.clean_section_references(section_text)
        return section_text

    def clean_section_references(self, section_text):
        """清理章节末尾的引用文献"""
        import re

        # 移除章节末尾的引用文献模式
        patterns = [
            r'\n\[1\].*?东方财富.*?\n',
            r'\n\[2\].*?同花顺.*?主营介绍.*?\n',
            r'\n\[3\].*?同花顺.*?股东信息.*?\n',
            r'\n\[4\].*?搜索引擎.*?\n',
            r'\n\[1\].*?东方财富.*?$',
            r'\n\[2\].*?同花顺.*?主营介绍.*?$',
            r'\n\[3\].*?同花顺.*?股东信息.*?$',
            r'\n\[4\].*?搜索引擎.*?$',
            # 更通用的模式：移除连续的引用列表
            r'\n(?:\[[0-9]+\].*?\n){2,}$',
            r'\n(?:\[[0-9]+\].*?\n){2,}',
        ]

        cleaned_text = section_text
        for pattern in patterns:
            cleaned_text = re.sub(pattern, '\n', cleaned_text, flags=re.MULTILINE | re.DOTALL)

        # 移除末尾多余的空行
        cleaned_text = cleaned_text.rstrip() + '\n'

        return cleaned_text

    def generate_references_section(self):
        """生成统一的引用文献章节"""
        references = """
## 引用文献

[1] 东方财富-港股-财务报表: https://emweb.securities.eastmoney.com/PC_HKF10/FinancialAnalysis/index
[2] 同花顺-主营介绍: https://basic.10jqka.com.cn/new/000066/operate.html
[3] 同花顺-股东信息: https://basic.10jqka.com.cn/HK0020/holder.html
[4] 搜索引擎: https://tavily.com
[5] akshare金融数据接口: https://akshare.akfamily.xyz/
[6] 公司年报及公告: 各大财经网站及交易所官网
[7] 行业研究报告: 各大券商研究所公开发布的行业分析报告

## 免责声明

本报告基于公开信息和自动化数据采集生成，仅供参考，不构成投资建议。投资者应当根据自身情况独立判断投资决策并承担相应风险。报告中的数据和分析结果可能存在滞后性，请以最新公开信息为准。
"""
        return references
    
    def save_markdown(self, content, output_file):
        """保存markdown文件"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"\n📁 深度财务研报分析已保存到: {output_file}")
    
    def format_markdown(self, output_file):
        """格式化markdown文件"""
        try:
            import subprocess
            format_cmd = ["mdformat", output_file]
            subprocess.run(format_cmd, check=True, capture_output=True, text=True, encoding='utf-8')
            print(f"✅ 已用 mdformat 格式化 Markdown 文件: {output_file}")
        except Exception as e:
            print(f"[提示] mdformat 格式化失败: {e}\n请确保已安装 mdformat (pip install mdformat)")
    
    def convert_to_docx(self, output_file, docx_output=None):
        """转换为Word文档"""
        if docx_output is None:
            docx_output = output_file.replace('.md', '.docx')
        try:
            import subprocess
            import os
            pandoc_cmd = [
                "pandoc",
                output_file,
                "-o",
                docx_output,
                "--standalone",
                "--resource-path=.",
                "--extract-media=."
            ]
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            subprocess.run(pandoc_cmd, check=True, capture_output=True, text=True, encoding='utf-8', env=env)
            print(f"\n📄 Word版报告已生成: {docx_output}")
        except subprocess.CalledProcessError as e:
            print(f"[提示] pandoc转换失败。错误信息: {e.stderr}")
            print("[建议] 检查图片路径是否正确，或使用 --extract-media 选项")
        except Exception as e:
            print(f"[提示] 若需生成Word文档，请确保已安装pandoc。当前转换失败: {e}")
    
    # ========== 图片处理相关方法 ==========
    
    def ensure_dir(self, path):
        """确保目录存在"""
        if not os.path.exists(path):
            os.makedirs(path)
    
    def is_url(self, path):
        """判断是否为URL"""
        return path.startswith('http://') or path.startswith('https://')
    
    def download_image(self, url, save_path):
        """下载图片"""
        try:
            resp = requests.get(url, stream=True, timeout=10)
            resp.raise_for_status()
            with open(save_path, 'wb') as f:
                for chunk in resp.iter_content(1024):
                    f.write(chunk)
            return True
        except Exception as e:
            print(f"[下载失败] {url}: {e}")
            return False
    
    def copy_image(self, src, dst):
        """复制图片"""
        try:
            shutil.copy2(src, dst)
            return True
        except Exception as e:
            print(f"[复制失败] {src}: {e}")
            return False
    
    def find_image_in_outputs(self, filename):
        """在outputs目录的所有session中查找图片文件"""
        outputs_dir = "./outputs"
        if not os.path.exists(outputs_dir):
            return None

        # 遍历所有session目录
        for session_dir in os.listdir(outputs_dir):
            session_path = os.path.join(outputs_dir, session_dir)
            if os.path.isdir(session_path):
                img_path = os.path.join(session_path, filename)
                if os.path.exists(img_path):
                    return img_path
        return None

    def extract_images_from_markdown(self, md_path, images_dir, new_md_path):
        """从markdown中提取图片"""
        self.ensure_dir(images_dir)
        with open(md_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 匹配 ![alt](path) 形式的图片
        pattern = re.compile(r'!\[[^\]]*\]\(([^)]+)\)')
        matches = pattern.findall(content)
        used_names = set()
        replace_map = {}
        not_exist_set = set()

        for img_path in matches:
            img_path = img_path.strip()
            # 取文件名
            if self.is_url(img_path):
                filename = os.path.basename(urlparse(img_path).path)
            else:
                filename = os.path.basename(img_path)
            # 防止重名
            base, ext = os.path.splitext(filename)
            i = 1
            new_filename = filename
            while new_filename in used_names:
                new_filename = f"{base}_{i}{ext}"
                i += 1
            used_names.add(new_filename)
            new_img_path = os.path.join(images_dir, new_filename)
            # 下载或复制
            img_exists = True
            if self.is_url(img_path):
                success = self.download_image(img_path, new_img_path)
                if not success:
                    img_exists = False
            else:
                # 首先尝试原始路径
                abs_img_path = img_path
                if not os.path.isabs(img_path):
                    abs_img_path = os.path.join(os.path.dirname(md_path), img_path)

                # 如果原始路径不存在，尝试在outputs目录中查找
                if not os.path.exists(abs_img_path):
                    found_path = self.find_image_in_outputs(filename)
                    if found_path:
                        abs_img_path = found_path
                        print(f"[信息] 在outputs目录中找到图片: {found_path}")
                    else:
                        print(f"[警告] 本地图片不存在: {img_path}")
                        img_exists = False

                if img_exists and os.path.exists(abs_img_path):
                    success = self.copy_image(abs_img_path, new_img_path)
                    if not success:
                        img_exists = False
                else:
                    img_exists = False

            # 记录替换
            if img_exists:
                replace_map[img_path] = f'./images/{new_filename}'
            else:
                not_exist_set.add(img_path)

        # 替换 markdown 内容，保留图片引用但更新路径
        def replace_func(match):
            orig = match.group(1).strip()
            if orig in not_exist_set:
                # 保留图片引用，但标注为未找到
                alt_text = match.group(0).split('](')[0][2:]  # 提取alt文本
                return f"![{alt_text}](图片未找到: {orig})"
            return match.group(0).replace(orig, replace_map.get(orig, orig))

        new_content = pattern.sub(replace_func, content)
        with open(new_md_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print(f"图片处理完成！新文件: {new_md_path}")


def main():
    """主函数"""
    import argparse
    
    # 添加命令行参数支持
    parser = argparse.ArgumentParser(description='整合的金融研报生成器')
    parser.add_argument('--search-engine', choices=['ddg', 'sogou'], default='sogou',
                       help='搜索引擎选择: ddg (DuckDuckGo) 或 sogou (搜狗), 默认: ddg')
    parser.add_argument('--company', default='商汤科技', help='目标公司名称')
    parser.add_argument('--code', default='00020', help='股票代码')
    parser.add_argument('--market', default='HK', help='市场代码')
    
    args = parser.parse_args()
    
    # 创建生成器实例
    generator = IntegratedResearchReportGenerator(
        target_company=args.company,
        target_company_code=args.code, 
        target_company_market=args.market,
        search_engine=args.search_engine
    )
    
    # 运行完整流程
    basic_report = generator.run_full_pipeline()

    print("\n" + "="*100)
    print("🎯 程序执行完毕！生成的文件：")
    print(f"📊 基础分析报告: {basic_report}")
    print("="*100)


def generate_final_report_from_md(md_file_path):
    """直接从md文件生成final_report的便捷函数"""
    # 创建生成器实例
    generator = IntegratedResearchReportGenerator(
        target_company="商汤科技",
        target_company_code="00020",
        target_company_market="HK",
        search_engine="tavily"
    )

    # 直接调用第二阶段生成深度研报
    final_report = generator.stage2_deep_report_generation(md_file_path)
    return final_report

if __name__ == "__main__":
    # 检查是否有命令行参数指定md文件
    import sys
    if len(sys.argv) > 1 and sys.argv[1].endswith('.md'):
        md_file = sys.argv[1]
        print(f"正在从 {md_file} 生成深度研报...")
        final_report = generate_final_report_from_md(md_file)
        print(f"深度研报已生成: {final_report}")
    else:
        main()
