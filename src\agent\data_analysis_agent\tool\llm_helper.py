# -*- coding: utf-8 -*-
"""
LLM调用辅助模块
"""

import asyncio
import yaml
from ..config.llm_config import LLMConfig
from .fallback_openai_client import AsyncFallbackOpenAIClient

class LLMHelper:
    """LLM调用辅助类，支持同步和异步调用"""
    
    def __init__(self, config: LLMConfig = None):
        self.config = config

        # 如果没有配置API key，使用默认值避免错误
        api_key = config.api_key or "dummy-key"
        base_url = config.base_url or "https://api.openai.com/v1"

        self.client = AsyncFallbackOpenAIClient(
            primary_api_key=api_key,
            primary_base_url=base_url,
            primary_model_name=config.model,
            fallback_api_key=config.fallback_api_key,
            fallback_base_url=config.fallback_base_url,
            fallback_model_name=config.fallback_model
        )
    
    async def async_call(self, prompt: str, system_prompt: str = None, max_tokens: int = None, temperature: float = None) -> str:
        """异步调用LLM"""
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        kwargs = {}
        if max_tokens is not None:
            kwargs['max_tokens'] = max_tokens
        else:
            kwargs['max_tokens'] = self.config.max_tokens
            
        if temperature is not None:
            kwargs['temperature'] = temperature
        else:
            kwargs['temperature'] = self.config.temperature
            
        try:
            # 添加超时控制
            import asyncio
            response = await asyncio.wait_for(
                self.client.chat_completions_create(
                    messages=messages,
                    **kwargs
                ),
                timeout=120.0  # 2分钟超时
            )
            return response.choices[0].message.content
        except asyncio.TimeoutError:
            print("⚠️ LLM调用超时，请检查网络连接或稍后重试")
            raise Exception("LLM调用超时")
        except KeyboardInterrupt:
            print("⚠️ 用户中断了操作")
            raise
        except asyncio.CancelledError:
            print("⚠️ LLM调用被取消")
            raise Exception("LLM调用被取消")
        except Exception as e:
            print(f"❌ LLM调用失败: {e}")
            raise Exception(f"LLM调用失败: {e}")
    def call(self, prompt: str, system_prompt: str = None, max_tokens: int = None, temperature: float = None) -> str:
        """同步调用LLM"""
        try:
            # 尝试获取当前事件循环
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行（如在Jupyter中），使用nest_asyncio
                try:
                    import nest_asyncio
                    nest_asyncio.apply()
                    return asyncio.run(self.async_call(prompt, system_prompt, max_tokens, temperature))
                except ImportError:
                    # 如果没有nest_asyncio，使用create_task
                    task = asyncio.create_task(self.async_call(prompt, system_prompt, max_tokens, temperature))
                    # 等待任务完成
                    import concurrent.futures
                    import threading
                    
                    result = None
                    exception = None
                    
                    def run_task():
                        nonlocal result, exception
                        try:
                            new_loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(new_loop)
                            result = new_loop.run_until_complete(self.async_call(prompt, system_prompt, max_tokens, temperature))
                            new_loop.close()
                        except Exception as e:
                            exception = e
                    
                    thread = threading.Thread(target=run_task)
                    thread.start()
                    thread.join()
                    
                    if exception:
                        raise exception
                    return result
            else:
                # 如果事件循环未运行，直接使用asyncio.run
                return asyncio.run(self.async_call(prompt, system_prompt, max_tokens, temperature))
        except RuntimeError:
            # 如果没有事件循环，创建新的
            return asyncio.run(self.async_call(prompt, system_prompt, max_tokens, temperature))
    
    def parse_yaml_response(self, response: str) -> dict:
        """解析YAML格式的响应"""
        try:
            # 提取```yaml和```之间的内容
            if '```yaml' in response:
                start = response.find('```yaml') + 7
                end = response.find('```', start)
                if end == -1:  # 如果没有找到结束标记，使用整个剩余内容
                    yaml_content = response[start:].strip()
                else:
                    yaml_content = response[start:end].strip()
            elif '```' in response:
                start = response.find('```') + 3
                end = response.find('```', start)
                if end == -1:  # 如果没有找到结束标记，使用整个剩余内容
                    yaml_content = response[start:].strip()
                else:
                    yaml_content = response[start:end].strip()
            else:
                yaml_content = response.strip()

            # 预处理YAML内容，修复Windows路径问题
            yaml_content = self._fix_windows_paths_in_yaml(yaml_content)

            # 尝试解析YAML
            parsed_data = yaml.safe_load(yaml_content)

            # 如果解析结果不是字典，尝试简单解析
            if not isinstance(parsed_data, dict):
                return self._simple_parse_response(response)

            return parsed_data

        except yaml.YAMLError as e:
            print(f"⚠️ YAML解析失败: {e}")
            print(f"📝 尝试简单解析...")
            return self._simple_parse_response(response)
        except Exception as e:
            print(f"❌ 响应解析异常: {e}")
            print(f"📝 原始响应前200字符: {response[:200]}...")
            return self._simple_parse_response(response)

    def _fix_windows_paths_in_yaml(self, yaml_content: str) -> str:
        """修复YAML中的Windows路径问题"""
        import re

        # 匹配双引号中的Windows路径模式，如 "D:\path\to\file"
        # 将反斜杠替换为正斜杠或使用原始字符串
        def replace_path(match):
            path = match.group(1)
            # 将反斜杠替换为正斜杠
            fixed_path = path.replace('\\', '/')
            return f'"{fixed_path}"'

        # 匹配 file_path: "D:\..." 这样的模式
        pattern = r'file_path:\s*"([^"]*\\[^"]*)"'
        yaml_content = re.sub(pattern, replace_path, yaml_content)

        # 也处理其他可能包含Windows路径的字段
        pattern = r'path:\s*"([^"]*\\[^"]*)"'
        yaml_content = re.sub(pattern, replace_path, yaml_content)

        # 处理没有引号的Windows路径
        pattern = r'file_path:\s*([A-Za-z]:[^"\s\n]+)'
        def replace_unquoted_path(match):
            path = match.group(1)
            fixed_path = path.replace('\\', '/')
            return f'file_path: "{fixed_path}"'
        yaml_content = re.sub(pattern, replace_unquoted_path, yaml_content)

        return yaml_content

    def _simple_parse_response(self, response: str) -> dict:
        """简单解析响应，提取基本信息"""
        result = {'action': 'generate_code'}  # 默认动作

        try:
            # 尝试提取action
            if 'action:' in response:
                lines = response.split('\n')
                for line in lines:
                    line = line.strip()
                    if line.startswith('action:'):
                        action_value = line.split(':', 1)[1].strip().strip('"\'')
                        result['action'] = action_value
                        break

            # 如果是collect_figures动作，尝试简单提取图片信息
            if result.get('action') == 'collect_figures':
                figures_to_collect = []
                lines = response.split('\n')
                current_figure = {}

                for line in lines:
                    line = line.strip()
                    if line.startswith('- figure_number:'):
                        # 保存之前的图片信息
                        if current_figure:
                            figures_to_collect.append(current_figure)
                        # 开始新的图片
                        current_figure = {'figure_number': line.split(':', 1)[1].strip()}
                    elif line.startswith('filename:') and current_figure:
                        current_figure['filename'] = line.split(':', 1)[1].strip().strip('"\'')
                    elif line.startswith('file_path:') and current_figure:
                        path = line.split(':', 1)[1].strip().strip('"\'')
                        # 规范化路径为当前操作系统格式
                        import os
                        path = os.path.normpath(path)
                        current_figure['file_path'] = path
                    elif line.startswith('description:') and current_figure:
                        current_figure['description'] = line.split(':', 1)[1].strip().strip('"\'')
                    elif line.startswith('analysis:') and current_figure:
                        current_figure['analysis'] = line.split(':', 1)[1].strip().strip('"\'')

                # 保存最后一个图片信息
                if current_figure:
                    figures_to_collect.append(current_figure)

                result['figures_to_collect'] = figures_to_collect

            # 尝试提取code
            if 'code:' in response:
                # 查找code:后的内容
                code_start = response.find('code:')
                if code_start != -1:
                    code_content = response[code_start + 5:].strip()
                    # 如果有|符号，说明是多行内容
                    if code_content.startswith('|'):
                        code_lines = []
                        lines = code_content.split('\n')[1:]  # 跳过第一行的|
                        for line in lines:
                            if line.strip() and not line.strip().startswith('next_steps:'):
                                code_lines.append(line)
                            elif line.strip().startswith('next_steps:'):
                                break
                        result['code'] = '\n'.join(code_lines)

            # 尝试提取reasoning
            if 'reasoning:' in response:
                lines = response.split('\n')
                for line in lines:
                    line = line.strip()
                    if line.startswith('reasoning:'):
                        reasoning_value = line.split(':', 1)[1].strip().strip('"\'')
                        result['reasoning'] = reasoning_value
                        break

            return result

        except Exception as e:
            print(f"⚠️ 简单解析也失败: {e}")
            return {'action': 'generate_code', 'error': str(e)}
    
    async def close(self):
        """关闭客户端"""
        await self.client.close()